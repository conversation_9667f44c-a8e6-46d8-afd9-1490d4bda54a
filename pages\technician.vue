<template>
	<view class="page">
		<tabbar :cur="1"></tabbar>
		<view class="header" id="header">
			<u-search placeholder="空调维修" v-model="keyword" :showAction="false" inputAlign="center"
				@focus="goUrl('/user/search')"></u-search>
		</view>
		<view class="main">
			<view class="left">
				<scroll-view scroll-y="true" class="scrollL">
					<view v-if="loading" class="loading">
						<text>加载中...</text>
					</view>
					<view v-else-if="error" class="error">
						<text>{{ error }}</text>
					</view>
					<view v-else-if="!categories.length" class="no-content">
						<text>暂无分类数据</text>
					</view>
					<view v-else class="left_item" v-for="category in categories" :key="category.id"
						@tap="selectCategory(category.id)">
						<view class="category_name" :class="{ active: selectedCategoryId === category.id }">
							{{ category.name }}
						</view>
						<view
							v-if="selectedCategoryId === category.id && category.children && category.children.length && expandSubCategories"
							class="sub_categories">
							<view class="sub_category" v-for="subCategory in category.children" :key="subCategory.id"
								@tap.stop="selectSubCategory(subCategory.id)"
								:class="{ active: activeSubCategoryId === subCategory.id }">
								{{ subCategory.name }}
							</view>
						</view>
					</view>
					<!-- 添加底部占位空间 -->
					<view class="bottom_placeholder"></view>
				</scroll-view>
			</view>

			<view class="right">
				<scroll-view scroll-y="true" class="scrollR" :scroll-into-view="scrollToId" @scroll="onScroll"
					:throttle="false">
					<view v-if="loading" class="loading">
						<text>加载中...</text>
					</view>
					<view v-else-if="error" class="error">
						<text>{{ error }}</text>
					</view>
					<view v-else-if="currentServices.length">
						<view class="right_box" v-for="service in currentServices" :key="service.id"
							:id="'service-' + service.id">
							<view class="title">{{ service.name }}</view>
							<view class="img">
								<view class="img_item" v-for="item in service.serviceList" :key="item.id"
									@click="goToDetails(item.id)">
									<image :src="item.cover || 'https://via.placeholder.com/144'" mode="aspectFill">
									</image>
									<view class="lname">{{ item.title }}</view>
								</view>
							</view>
						</view>
					</view>
					<view v-else class="no-content">
						<text>暂无服务</text>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	import tabbar from "@/components/tabbar.vue";
	import $api from "@/api/index.js";

	export default {
		components: {
			tabbar
		},
		data() {
			return {
				keyword: "",
				categories: [],
				selectedCategoryId: null,
				selectedSubCategoryId: null,
				activeSubCategoryId: null, // 新增：当前高亮的子类ID
				scrollToId: "",
				loading: false,
				error: null,
				tmplIds: [
					'',
					'',
					'',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				expandSubCategories: false,
				initialCategoryId: null,
				headerHeight: 0, // header高度
				isScrolling: false, // 是否正在滚动
			};
		},
		computed: {
			currentServices() {
				if (!this.selectedCategoryId) return [];

				const selectedCategory = this.categories.find(
					(cat) => String(cat.id) === String(this.selectedCategoryId)
				);
				if (!selectedCategory) return [];

				if (!selectedCategory.children || !selectedCategory.children.length) return [];

				if (!this.selectedSubCategoryId) {
					return selectedCategory.children.filter(
						(sub) => sub.serviceList && sub.serviceList.length > 0
					);
				}

				const selectedSubCategory = selectedCategory.children.find(
					(sub) => String(sub.id) === String(this.selectedSubCategoryId)
				);
				return selectedSubCategory && selectedSubCategory.serviceList ?
					[selectedSubCategory] :
					[];
			},
		},
		methods: {
			selectCategory(id) {

				id = String(id);
				if (this.selectedCategoryId === id) {
					this.expandSubCategories = !this.expandSubCategories;
					this.selectedSubCategoryId = null;
					this.activeSubCategoryId = null;
					this.scrollToId = "";
				} else {
					this.selectedCategoryId = id;
					this.selectedSubCategoryId = null;
					this.activeSubCategoryId = null;
					this.scrollToId = "";
					this.expandSubCategories = true;
				}

				console.log("Selected Category ID:", this.selectedCategoryId, "Expand Subcategories:", this
					.expandSubCategories);
			},
			selectSubCategory(id) {
				this.selectedSubCategoryId = String(id);
				this.activeSubCategoryId = String(id);
				const service = this.currentServices.find((s) => String(s.id) === String(id));
				if (service) {
					this.scrollToId = `service-${service.id}`;
				}
				console.log("Selected SubCategory ID:", this.selectedSubCategoryId, "Scroll To:", this.scrollToId);
			},
			// 滚动事件处理
			onScroll(e) {
				if (this.isScrolling) return;

				this.isScrolling = true;
				// 使用节流，避免频繁触发
				setTimeout(() => {
					this.checkVisibleService(e.detail.scrollTop);
					this.isScrolling = false;
				}, 50);
			},
			dingyue() {
				console.log('dingyue called');
				const allTmplIds = this.tmplIds;
				if (allTmplIds.length < 3) {
					console.error("Not enough template IDs available:", allTmplIds);
					// uni.showToast({
					// 	icon: 'none',
					// 	title: '模板ID不足'
					// });
					return;
				}
				const shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());
				const selectedTmplIds = shuffled.slice(0, 3);
				console.log("Selected template IDs:", selectedTmplIds);
				const templateData = selectedTmplIds.map((id, index) => ({
					templateId: id,
					templateCategoryId: index === 0 ? 10 : 5
				}));
				uni.requestSubscribeMessage({
					tmplIds: selectedTmplIds,
					success: (res) => {
						console.log('requestSubscribeMessage result:', res);
						this.templateCategoryIds = [];
						let count = 0;
						selectedTmplIds.forEach((templId, index) => {
							console.log(`Template ${templId} status: ${res[templId]}`);
							if (res[templId] === 'accept') {
								const templateCategoryId = templateData[index].templateCategoryId;
								if (templateCategoryId === 10) {
									for (let i = 0; i < 15; i++) {
										this.templateCategoryIds.push(templateCategoryId);
									}
								} else {
									this.templateCategoryIds.push(templateCategoryId);
								}
								console.log('Accepted message push for template:', templId);
							}
						});
						console.log('Updated templateCategoryIds:', this.templateCategoryIds);
					},
					fail: (err) => {}
				});
			},

			// 检查当前可见的服务区域
			async checkVisibleService(scrollTop) {
				if (!this.currentServices.length) return;

				try {
					// 获取header高度作为参考线
					if (!this.headerHeight) {
						const query = uni.createSelectorQuery().in(this);
						const headerInfo = await new Promise((resolve) => {
							query.select('#header').boundingClientRect((data) => {
								resolve(data);
							}).exec();
						});
						this.headerHeight = headerInfo ? headerInfo.height : 80; // 默认80px
					}

					// 获取所有服务区域的位置信息
					const query = uni.createSelectorQuery().in(this);
					const servicePositions = await new Promise((resolve) => {
						this.currentServices.forEach((service, index) => {
							query.select(`#service-${service.id}`).boundingClientRect();
						});
						query.exec((res) => {
							resolve(res);
						});
					});

					// 找到当前在header底部位置的服务区域
					let activeServiceId = null;
					const threshold = this.headerHeight + 20; // header底部 + 一些偏移

					for (let i = 0; i < servicePositions.length; i++) {
						const pos = servicePositions[i];
						if (pos && pos.top <= threshold && pos.bottom > threshold) {
							activeServiceId = this.currentServices[i].id;
							break;
						}
					}

					// 如果没有找到完全符合条件的，找最接近的
					if (!activeServiceId && servicePositions.length > 0) {
						let closestIndex = 0;
						let minDistance = Math.abs(servicePositions[0].top - threshold);

						for (let i = 1; i < servicePositions.length; i++) {
							const pos = servicePositions[i];
							if (pos) {
								const distance = Math.abs(pos.top - threshold);
								if (distance < minDistance && pos.top <= threshold + 100) {
									minDistance = distance;
									closestIndex = i;
								}
							}
						}
						activeServiceId = this.currentServices[closestIndex].id;
					}

					// 更新高亮的子类
					if (activeServiceId && String(activeServiceId) !== String(this.activeSubCategoryId)) {
						this.activeSubCategoryId = String(activeServiceId);
						console.log("Active SubCategory ID changed to:", this.activeSubCategoryId);
					}
				} catch (err) {
					console.error("Error in checkVisibleService:", err);
				}
			},
			goToDetails(id) {
				uni.navigateTo({
					url: `../user/commodity_details?id=${id}`,
					fail: (err) => {
						console.error("Navigation failed:", err);
						uni.showToast({
							title: "跳转失败: " + err.errMsg,
							icon: "none",
						});
					},
				});
			},
			goUrl(url) {
				uni.navigateTo({
					url
				});
			},
			async getList(city) {
				if (!city || !city.position) {
					this.error = "未获取到城市信息";
					uni.showToast({
						title: "未获取到城市信息",
						icon: "none",
					});
					return;
				}

				this.loading = true;
				this.error = null;
				try {
					const response = await $api.service.setserviceCate(city.position);
					console.log(response)
					let categories = [];
					if (Array.isArray(response)) {
						categories = response;
					} else if (response.data && Array.isArray(response.data)) {
						categories = response.data;
					} else {
						throw new Error("Invalid or empty data from API");
					}

					this.categories = Object.freeze(
						categories.map(cat => ({
							...cat,
							id: String(cat.id),
							children: cat.children ? cat.children.map(sub => ({
								...sub,
								id: String(sub.id)
							})) : []
						}))
					);

					this.setInitialCategory();
				} catch (err) {
					this.error = "数据加载失败: " + err.message;
					console.error("Error in getList:", err);
					uni.showToast({
						title: this.error,
						icon: "none",
					});
				} finally {
					this.loading = false;
				}
			},
			setInitialCategory() {
				if (this.categories.length > 0) {
					console.log("Initial Category ID from onLoad:", this.initialCategoryId);
					const targetId = this.initialCategoryId ? String(this.initialCategoryId) : null;
					const categoryExists = targetId && this.categories.some(cat => String(cat.id) === targetId);

					if (categoryExists) {
						this.selectedCategoryId = targetId;
						this.expandSubCategories = true;
					} else {
						this.selectedCategoryId = String(this.categories[0].id);
						this.expandSubCategories = false;
					}

					// 初始化第一个子类为高亮
					this.$nextTick(() => {
						if (this.currentServices.length > 0) {
							this.activeSubCategoryId = String(this.currentServices[0].id);
						}
					});

					console.log("Set selectedCategoryId:", this.selectedCategoryId, "expandSubCategories:", this
						.expandSubCategories);
				} else {
					this.error = "分类数据为空";
					uni.showToast({
						title: "分类数据为空",
						icon: "none",
					});
				}
			},
		},
		onLoad(e) {
			console.log("onLoad params:", JSON.stringify(e, null, 2));
			this.initialCategoryId = e.id ? String(e.id) : null;

			let city = uni.getStorageSync("city");
			if (!city) {
				city = {
					position: "阜阳市"
				};
			}
			console.log("City:", JSON.stringify(city, null, 2));

			this.getList(city);
		},
	};
</script>

<style scoped>
	.page {
		height: 100vh;
		overflow: hidden;
		background-color: #f8f8f8;
		/* 确保 tabbar 不遮挡内容 */
		box-sizing: border-box;
		padding-bottom: 80rpx;
	}

	.header {
		padding: 0 15rpx;
		display: flex;
		align-items: center;
		background-color: #fff;
		height: 10vh;
		position: relative;
		z-index: 10;
	}

	.main {
		display: flex;
		/* 计算剩余高度 */
		height: calc(90vh - 80rpx);
	}

	.left {
		width: 190rpx;
		background-color: #f8f8f8;
		/* 确保 left 容器高度和 main 一致 */
		height: 100%;
	}

	.scrollL {
		/* 修复：设置准确的滚动容器高度 */
		height: 100%;
		overflow-y: auto;
		/* 确保内容可以完整滚动 */
		padding-bottom: 20rpx;
	}

	.left_item {
		padding: 0;
		min-height: 100rpx;
		font-size: 28rpx;
		font-weight: 500;
		color: #333;
		transition: all 0.2s;
		display: flex;
		flex-direction: column;
	}

	.category_name {
		height: 100rpx;
		width: 100%;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		border-left: 6rpx solid transparent;
		transition: all 0.2s;
		/* 防止文本换行 */
		white-space: nowrap;
		box-sizing: border-box;
	}

	.category_name.active {
		color: #fff;
		background-color: #2e80fe;
		border-left-color: #2e80fe;
	}

	.sub_categories {
		width: 100%;
		padding-left: 35rpx;
		font-size: 20rpx;
		display: flex;
		flex-direction: column;
		background-color: #fff;
	}

	.sub_category {
		min-height: 80rpx;
		line-height: 80rpx;
		font-size: 26rpx;
		font-weight: 400;
		color: #666;
		transition: color 0.2s;
		position: relative;
	}

	.sub_category.active {
		color: #2e80fe;
		font-weight: 500;
	}

	.sub_category.active::before {
		content: '';
		position: absolute;
		left: -15rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 30rpx;
		background-color: #2e80fe;
		border-radius: 3rpx;
	}

	/* 新增：底部占位空间样式 */
	.bottom_placeholder {
		height: 40rpx;
		width: 100%;
	}

	.right {
		flex: 1;
		background-color: #fff;
		border-radius: 12rpx 12rpx 0 0;
		margin-left: 10rpx;
		/* 确保 right 容器高度和 main 一致 */
		height: 100%;
		overflow: hidden; /* 防止内部滚动条影响布局 */
	}

	.scrollR {
		height: 100%;
		overflow-y: auto;
	}

	.right_box {
		padding: 24rpx 9rpx;
	}

	.right_box .title {
		padding-left: 15rpx;
		font-size: 28rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 20rpx;
	}

	.img {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
	}

	.img_item {
		width: 220rpx;
		margin: 10rpx 15rpx;
		background-color: #fff;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx;
		transition: transform 0.2s;
		box-sizing: border-box;
	}

	.img_item:hover {
		transform: translateY(-4rpx);
	}

	.img_item image {
		width: 144rpx;
		height: 144rpx;
		border-radius: 12rpx;
		margin-bottom: 12rpx;
	}

	.lname {
		font-size: 24rpx;
		font-weight: 400;
		color: #333;
		text-align: center;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		width: 100%;
	}

	.no-content,
	.loading,
	.error {
		text-align: center;
		color: #999;
		font-size: 28rpx;
		padding: 40rpx;
	}

	.error {
		color: #ff4d4f;
	}
</style>