<template>
	<view class="pages-mine">
		<!-- Header Section -->
		<view class="header" v-if="!isLoading">
			<view class="header-content">
				<view class="avatar_view">
					<image mode="aspectFill" class="avatar" :src="userInfo.avatarUrl"></image>
				</view>
				<view class="user-info">
					<view class="user-info-logged">
						<view class="nickname">
							{{ userInfo.nickName }}
						</view>
						<view class="phone-number" v-if="userInfo.phone">
							{{ userInfo.phone }}
						</view>
						<view class="status-badge" :class="statusBadgeClass" v-if="userInfo.status !== undefined">
							{{ statusText }}
						</view>
					</view>
				</view>
				<view @click="navigateTo('../shifu/userProfile')" class="settings">
					<i class="iconfont icon-xitong text-bold"></i>
				</view>
			</view>
		</view>

		<!-- Action Buttons Section (My Orders) -->
		<view class="mine-menu-list box-shadow fill-base box1">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">我的订单</view>
			</view>
			<view @click="dingyue()" class="flex-warp pt-lg pb-lg">
				<view class="order-item" v-for="(item, index) in orderList2" :key="index"
					@tap="handleNavigate(item.url)">
					<view class="icon-container">
						<u-icon :name="item.icon" color="#448cfb" size="28"></u-icon>
						<view class="number-circle" v-if="item.count > 0">{{ item.count }}</view>
					</view>
					<view class="mt-sm">{{ item.text }}</view>
				</view>
			</view>
		</view>

		<!-- Commonly Used Functions -->
		<view @click="dingyue()" class="mine-menu-list box-shadow fill-base">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">常用功能</view>
			</view>
			<view class="flex-warp pt-lg pb-lg">
				<view class="order-item" v-for="(item, index) in orderList3" :key="index"
					@tap="handleNavigate(item.url)">
					<u-icon :name="item.icon" color="#448cfb" size="28"></u-icon>
					<view class="mt-sm">{{ item.text }}</view>
				</view>
			</view>
		</view>

		<!-- Other Functions -->
		<view class="mine-menu-list box-shadow fill-base">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">其他功能</view>
			</view>
			<view class="flex-warp pt-lg pb-lg">
				<view class="order-item" @tap="handleNavigate('/shifu/skills')">
					<u-icon name="plus-square-fill" color="#448cfb" size="28"></u-icon>
					<view class="mt-sm">技能标签</view>
				</view>
				<view class="order-item" @tap="handleNavigate('/shifu/Professiona')">
					<u-icon name="order" color="#448cfb" size="28"></u-icon>
					<view class="mt-sm">技能证书</view>
				</view>
				<view class="order-item" @tap="handleNavigate('/user/promotion')">
					<u-icon name="red-packet-fill" color="#E41F19" size="28"></u-icon>
					<view style="color: #E41F19;" class="mt-sm">邀请有礼</view>
				</view>
			</view>
		</view>

		<!-- Spacer -->
		<view class="spacer"></view>

		<!-- Tool List Section - Modified to Grid Layout -->
		<view class="mine-tool-grid fill-base">
			<view class="grid-container">
				<view class="grid-item" v-for="(item, index) in toolList2" :key="index" @tap="handleNavigate(item.url)">
					<view class="grid-icon-container">
						<u-icon :name="item.icon" :color="item.iconColor" size="28"></u-icon>
					</view>
					<view class="grid-text">{{ item.text }}</view>
				</view>

				<view class="grid-item" @tap="navigateTo('../pages/service')">
					<view class="grid-icon-container switch-identity">
						<u-icon name="man-add" color="#E41F19" size="28"></u-icon>
					</view>
					<view style="color: #E41F19;" class="grid-text">切换用户版</view>
				</view>

				<view class="grid-item">
					<button class="contact-btn-wrapper" open-type="contact" bindcontact="handleContact"
						session-from="sessionFrom">
						<view class="grid-icon-container switch-identity">
							<u-icon name="server-man" color="#448cfb" size="28"></u-icon>
						</view>
						<view class="grid-text">客服</view>
					</button>
				</view>
			</view>
		</view>

		<!-- Tabbar -->
		<tabbar cur="1"></tabbar>
	</view>
</template>

<script>
	import tabbar from "@/components/tabbarsf.vue";
	import {
		mapState,
		mapMutations
	} from "vuex";

	export default {
		components: {
			tabbar
		},
		data() {
			return {
				permissionID: '',
				isLoading: true,
				options: {},
				shifustatus: '',
				shiInfoResponse: null, // New reactive state for shiInfoResponse
				tmplIds: [
					'',
					'',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				inviteCode: '',
				coach_info: {},
				orderList2: [{
						icon: 'order',
						text: '全部',
						url: '/shifu/master_my_order?tab=0',
						count: 0
					},
					{
						icon: 'bell',
						text: '待上门',
						url: '/shifu/master_my_order?tab=3',
						count: 0
					},
					{
						icon: 'hourglass-half-fill',
						text: '待服务',
						url: '/shifu/master_my_order?tab=5',
						count: 0
					},
					{
						icon: 'clock',
						text: '服务中',
						url: '/shifu/master_my_order?tab=6',
						count: 0
					},
					{
						icon: 'thumb-up',
						text: '已完成',
						url: '/shifu/master_my_order?tab=7',
						count: 0
					},
					{
						icon: 'chat-fill',
						text: '售后',
						url: '/shifu/master_my_order?tab=8',
						count: 0
					},
				],
				orderList3: [{
						icon: 'red-packet',
						text: '服务收入',
						url: '/shifu/income'
					},
					{
						icon: 'file-text-fill',
						text: '报价列表',
						url: '/shifu/master_bao_list'
					},
					{
						icon: 'rmb-circle',
						text: '保证金',
						url: '/shifu/Margin'
					}
				],
				toolList2: [{
						icon: 'plus-people-fill',
						text: '师傅入驻',
						url: '/shifu/Settle',
						iconColor: '#448cfb'
					},
					{
						icon: 'edit-pen',
						text: '编辑师傅资料',
						url: '/shifu/master_Info',
						iconColor: '#448cfb'
					},
				]
			};
		},
		computed: {
			...mapState({
				storeUserInfo: state => state.user.userInfo || {},
				userPageType: state => state.user.userPageType,
				mineInfo: state => state.user.mineInfo,
				token: state => state.user.autograph || ''
			}),
			isLoggedIn() {
				return !!this.token;
			},
			userInfo() {
				const shiInfo = this.shiInfoResponse || (uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {});
				return {
					phone: shiInfo.mobile || this.storeUserInfo.phone || uni.getStorageSync('phone') || '',
					avatarUrl: shiInfo.avatarUrl || this.storeUserInfo.avatarUrl || uni.getStorageSync('avatarUrl') ||
						'/static/mine/default_user.png',
					nickName: shiInfo.coachName || this.storeUserInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
					userId: shiInfo.id || this.storeUserInfo.userId || uni.getStorageSync('userId') || '',
					pid: shiInfo.pid || this.storeUserInfo.pid || uni.getStorageSync('pid') || '',
					status: Number(shiInfo.status) !== undefined ? Number(shiInfo.status) : -1
				};
			},
			statusText() {
				switch (this.shifustatus) {
					case -1:
						return '未入驻师傅';
					case 1:
						return '审核中';
					case 2:
						return '已认证';
					case 4:
						return '审核驳回';
					default:
						return '';
				}
			},
			statusBadgeClass() {
				return {
					'status-not-registered': this.userInfo.status === -1,
					'status-pending': this.userInfo.status === 1,
					'status-approved': this.userInfo.status === 2,
					'status-rejected': this.userInfo.status === 4
				};
			}
		},
		methods: {
			...mapMutations(['updateUserItem']),
			async getHighlight() {
				const shiInfoid = this.shiInfoResponse || (uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : null);
				if (!shiInfoid || !shiInfoid.id) {
					console.log('No userId, skipping getHighlight');
					return;
				}
				try {
					const res = await this.$api.service.getHighlight({
						userId: shiInfoid.id,
						role: 2
					});
					console.log('getHighlight response:', res);
					const updatedOrderList = this.orderList2.map((item, index) => ({
						...item,
						count: index === 0 ? (res && res.countOrder ? res.countOrder : 0) : index === 1 ? (
							res && res.daiShangMen ? res.daiShangMen : 0) : index === 2 ? (res && res
							.daiFuWu ? res.daiFuWu : 0) : index === 3 ? (res && res.fuWuZhong ? res
							.fuWuZhong : 0) : index === 4 ? (res && res.yiWanCheng ? res.yiWanCheng :
							0) : index === 5 ? (res && res.shouHou ? res.shouHou : 0) : 0
					}));
					this.orderList2 = updatedOrderList;
				} catch (err) {
					console.error('getHighlight error:', err);
				}
			},

			dingyue() {
				console.log('dingyue called');
				const allTmplIds = this.tmplIds;
				if (allTmplIds.length < 3) {
					console.error("Not enough template IDs available:", allTmplIds);
					return;
				}
				const shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());
				const selectedTmplIds = shuffled.slice(0, 3);
				console.log("Selected template IDs:", selectedTmplIds);
				const templateData = selectedTmplIds.map((id, index) => ({
					templateId: id,
					templateCategoryId: index === 0 ? 10 : 5
				}));
				uni.requestSubscribeMessage({
					tmplIds: selectedTmplIds,
					success: (res) => {
						console.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);
						const hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');
						const hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
						if (hasRejection && !hasShownModal) {
							uni.showModal({
								title: '提示',
						content: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收用户订单通知。',
						cancelText: '取消',
						confirmText: '去开启',
						confirmColor: '#007AFF',
						success: (modalRes) => {
							uni.setStorageSync('hasShownSubscriptionModal', true);
							if (modalRes.confirm) {
								uni.openSetting({
									withSubscriptions: true
								});
							} else if (modalRes.cancel) {
								uni.setStorageSync('hasCanceledSubscription', true);
							}
						}
					});
					}
					this.templateCategoryIds = [];
					selectedTmplIds.forEach((templId, index) => {
						console.log(`Template ${templId} status: ${res[templId]}`);
						if (res[templId] === 'accept') {
							const templateCategoryId = templateData[index].templateCategoryId;
							if (templateCategoryId === 10) {
								for (let i = 0; i < 15; i++) {
									this.templateCategoryIds.push(templateCategoryId);
								}
							} else {
								this.templateCategoryIds.push(templateCategoryId);
							}
							console.log('Accepted message push for template:', templId);
						}
					});
					console.log('Updated templateCategoryIds:', this.templateCategoryIds);
				},
				fail: (err) => {
					console.error('requestSubscribeMessage failed:', err);
				}
			});
		},

			async fetchShifuInfo() {
				try {
					this.isLoading = true;
					const shiInfoResponse = await this.$api.shifu.getMaster();
					console.log(shiInfoResponse);
					if (!shiInfoResponse || typeof shiInfoResponse !== 'object') {
						throw new Error('获取师傅状态失败: 响应数据无效');
					}

					this.shiInfoResponse = shiInfoResponse; // Store response in reactive state

					const userInfo = {
						mobile: shiInfoResponse.mobile || '',
						avatarUrl: shiInfoResponse.avatarUrl || this.storeUserInfo.avatarUrl || uni.getStorageSync(
							'avatarUrl') || '/static/mine/default_user.png',
						coachName: shiInfoResponse.coachName || this.storeUserInfo.nickName || uni.getStorageSync(
							'nickName') || '微信用户',
						id: shiInfoResponse.id || this.storeUserInfo.userId || uni.getStorageSync('userId') || '',
						pid: this.storeUserInfo.pid || uni.getStorageSync('pid') || '',
						status: Number(shiInfoResponse.status) || -1,
						messagePush: Number(shiInfoResponse.messagePush) || -1
					};

					uni.setStorageSync('shiInfo', JSON.stringify(userInfo));
					this.updateUserItem({
						key: 'shiInfo',
						val: userInfo
					});
					this.getshifustatus();

					const modalShownKey = `certificationModalShown_${userInfo.id}_${userInfo.status}`;
					const hasShownModal = uni.getStorageSync(modalShownKey);

					if (!hasShownModal && (userInfo.status === -1 || userInfo.status === 4)) {
						this.showCertificationPopup();
						uni.setStorageSync(modalShownKey, 'true');
					}
				} catch (error) {
					console.error('fetchShifuInfo error:', error);
					this.shiInfoResponse = null; // Reset on error
					const defaultUserInfo = {
						mobile: '',
						avatarUrl: this.storeUserInfo.avatarUrl || uni.getStorageSync('avatarUrl') ||
							'/static/mine/default_user.png',
						coachName: this.storeUserInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
						id: this.storeUserInfo.userId || uni.getStorageSync('userId') || '',
						pid: this.storeUserInfo.pid || uni.getStorageSync('pid') || '',
						status: -1
					};

					uni.setStorageSync('shiInfo', JSON.stringify(defaultUserInfo));
					this.updateUserItem({
						key: 'shiInfo',
						val: defaultUserInfo
					});
					this.getshifustatus();

					const modalShownKey = `certificationModalShown_${defaultUserInfo.id}_${defaultUserInfo.status}`;
					const hasShownModal = uni.getStorageSync(modalShownKey);

					if (!hasShownModal && defaultUserInfo.status === -1) {
						this.showCertificationPopup();
						uni.setStorageSync(modalShownKey, 'true');
					}
				} finally {
					this.isLoading = false;
				}
			},
			getshifustatus() {
				const shiInfo = this.shiInfoResponse || (uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {});
				this.shifustatus = shiInfo.status;
				console.log(shiInfo);
			},
			seeinfo() {
				uni.openSetting({
					success(res) {
						console.log(res);
					}
				});
			},
			showCertificationPopup() {
				console.log(this.userInfo.status);
				if (this.userInfo.status === -1 || this.userInfo.status === 4) {
					uni.showModal({
						title: '提示',
						content: this.userInfo.status === -1 ? '您尚未成为师傅，是否前往认证？' : '您的师傅认证被驳回，是否重新认证？',
						confirmText: '去认证',
						cancelText: '再想想',
						cancelable: true,
						success: (res) => {
							if (res.confirm) {
								const targetUrl = this.userInfo.status === -1 ? '/shifu/Settle' :
									'/shifu/Settle';
								uni.navigateTo({
									url: targetUrl,
									fail(err) {
										console.error('Navigation to certification failed:', err);
										uni.showToast({
											title: '跳转认证页面失败',
											icon: 'none'
										});
									}
								});
							}
						},
						fail: (err) => {
							console.error('Modal failed:', err);
						}
					});
				}
			},
			handleNavigate(url) {
				if (url === '/shifu/Settle') {
					this.navigateTo(url);
					return;
				}

				if (url === '/user/promotion') {
					this.navigateTo(url);
					return;
				}
				if (url === '/shifu/master_Info') {
					this.navigateTo(url);
					return;
				}
				if (this.shifustatus === -1 || this.shifustatus === 4) {
					uni.showToast({
						title: '你还不是师傅',
						icon: 'none'
					});
				} else if (this.shifustatus === 1) {
					uni.showToast({
						title: '师傅状态在审核中',
						icon: 'none'
					});
				} else if (this.shifustatus === 2) {
					this.navigateTo(url);
				}
			},
			handleCallKf() {
				if (this.shifustatus === -1 || this.shifustatus === 4) {
					uni.showToast({
						title: '你还不是师傅',
						icon: 'none'
					});
				} else if (this.shifustatus === 1) {
					uni.showToast({
						title: '师傅状态在审核中',
						icon: 'none'
					});
				} else if (this.shifustatus === 2) {
					this.callkf();
				}
			},
			navigateTo(url) {
				if (url) {
					uni.navigateTo({
						url: url,
						fail(err) {
							console.error('Navigation failed:', err);
							uni.showToast({
								title: '页面跳转失败',
								icon: 'none'
							});
						}
					});
				} else {
					console.error('Navigation URL is empty or invalid');
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			},
			callkf() {
				uni.showToast({
					title: '联系客服功能待实现',
					icon: 'none'
				});
			}
		},
		async onLoad(options) {
			if (options.inviteCode) {
				console.log('Received inviteCode:', options.inviteCode);
				this.inviteCode = options.inviteCode;
				uni.setStorageSync('receivedInviteCode', options.inviteCode);
			}
			this.options = options;
			await this.fetchShifuInfo();
			await this.getHighlight();
		},
		async onShow() {
			await this.fetchShifuInfo();
			await this.getHighlight();
		},
		async onPullDownRefresh() {
			try {
				await this.fetchShifuInfo();
				await this.getHighlight();
			} finally {
				uni.stopPullDownRefresh();
			}
		}
	};
</script>

<style lang="scss">
	.floating-contact {
		position: fixed;
		bottom: 470rpx;
		right: 30rpx;
		z-index: 1000;
		background-color: #fff;
		border-radius: 50rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
		padding: 10rpx 20rpx;
		display: flex;
		align-items: center;
	}

	.contact-container {
		display: flex;
		align-items: center;
	}

	.contact-btn {
		background: none;
		border: none;
		color: #576b95;
		font-size: 30rpx;
		line-height: 1.5;
		padding: 10rpx 20rpx;
		display: flex;
		align-items: center;
	}

	.contact-btn:active {
		background-color: #ededee;
	}

	.pages-mine {
		min-height: 100vh;
		padding-bottom: 120rpx;

		.header {
			height: 292rpx;
			background-color: #599EFF;
			position: relative;

			.header-content {
				display: flex;
				align-items: center;
				padding: 40rpx 30rpx 0;
				position: relative;

				.avatar_view {
					width: 120rpx;
					height: 120rpx;
					border-radius: 50%;
					overflow: hidden;
					box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
					position: relative;

					.avatar {
						width: 100%;
						height: 100%;
						border-radius: 50%;
					}
				}

				.user-info {
					margin-left: 20rpx;
					color: #fff;

					.user-info-logged {
						display: flex;
						flex-direction: column;
						gap: 10rpx;
					}

					.nickname {
						font-size: 36rpx;
						font-weight: bold;
						display: flex;
						align-items: center;
						gap: 10rpx;
					}

					.phone-number {
						font-size: 28rpx;
						opacity: 0.9;
					}

					.status-badge {
						display: inline-block;
						padding: 8rpx 20rpx;
						font-size: 24rpx;
						line-height: 1.2;
						border-radius: 20rpx;
						color: #fff;
						text-align: center;
						margin-top: 10rpx;
						width: fit-content;
						box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
					}

					.status-not-registered {
						background-color: #b0b0b0;
					}

					.status-pending {
						background-color: #f4b400;
					}

					.status-approved {
						background-color: #f5a623;
					}

					.status-rejected {
						background-color: #f44336;
					}
				}

				.settings {
					position: absolute;
					right: 30rpx;
					top: 100rpx;
					color: #fff;
					font-size: 60rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					.icon-xitong {
						font-size: 40rpx;
						color: #fff;
					}
				}
			}
		}

		.box1 {
			margin-top: -20rpx;
			border-radius: 36rpx 36rpx 0 0;
			position: relative;
			z-index: 10;
		}

		.mine-menu-list {
			background-color: #fff;
			margin: 0 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

			.menu-title {
				height: 90rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 30rpx 0 40rpx;
				border-bottom: 1px solid #f0f0f0;

				.f-paragraph {
					font-size: 32rpx;
					color: #333;
					font-weight: bold;
				}
			}

			.flex-warp {
				display: flex;
				flex-wrap: wrap;
				padding: 10rpx 0;

				.order-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 33.3%;
					padding-bottom: 10rpx;
					margin-top: 15rpx;
					font-size: 25rpx;
					color: #666;
					transition: transform 0.2s;

					&:active {
						transform: scale(0.95);
					}

					.icon-container {
						position: relative;
						display: flex;
						align-items: center;
						justify-content: center;
					}

					.number-circle {
						position: absolute;
						top: -10rpx;
						right: -5rpx;
						width: 30rpx;
						height: 30rpx;
						background-color: #ff4d4f;
						color: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 20rpx;
						font-weight: bold;
					}
				}

				.mt-sm {
					margin-top: 16rpx;
				}
			}
		}

		.spacer {
			height: 20rpx;
			background-color: transparent;
		}

		.mine-tool-grid {
			background-color: #fff;
			margin: 0 20rpx 30rpx;
			border-radius: 12rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			padding: 30rpx;

			.grid-container {
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;
				gap: 20rpx;
			}

			.grid-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: calc(33.33% - 20rpx);
				min-width: 140rpx;
				transition: transform 0.2s ease;

				&:active {
					transform: scale(0.95);
				}

				.grid-icon-container {
					width: 80rpx;
					height: 80rpx;
					border-radius: 20rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					&.switch-identity {
						/* Specific styling for switch-identity icon */
					}
				}

				.grid-text {
					font-size: 25rpx;
					color: #333;
					font-weight: 500;
					text-align: center;
					line-height: 1.2;
					margin-bottom: 8rpx;
				}

				.grid-subtitle {
					font-size: 24rpx;
					color: #A1A1A1;
					text-align: center;
				}
			}
		}

		.flex-between {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
	}
</style>