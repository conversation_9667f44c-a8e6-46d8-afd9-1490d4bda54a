<template>
	<view class="page">
		<view class="header">
			<view class="header_item" v-for="(item, index) in list" :key="index" @click="handleHeader(item)">
				<view :style="currentIndex == item.value ? 'color:#2E80FE;' : ''">{{ item.name }}</view>
				<view class="blue" :style="currentIndex == item.value ? '' : 'background-color:#fff;'"></view>
			</view>
		</view>
		<view @click="dingyue()" class="main">
			<view class="main_item" v-for="(item, index) in orderList" :key="index" @click="goDetail(item)">
				<view class="head">
					<view class="no">单号：{{ item.orderCode }}</view>
					<view class="type">{{ item.payType == -1 ? '已取消' : pay_typeArr[item.payType] }}</view>
				</view>
				<view class="mid">
					<view class="lef">
						<image :src="item.goodsCover" mode=""></image>
						<text>{{ item.goodsName }}</text>
					</view>
					<view class="righ" v-if="item.payType == 7 || (item.payType == 7 && item.isAftermarket === 1)">
						<view>￥{{ item.coachServicePrice }}</view>
						<view>x{{ item.num }}</view>
					</view>
				</view>
				<view class="bot">
					<text>{{ $util.timestampToTime(item.createTime * 1000) }}</text>
					<view class="qzf" v-if="item.payType === 3" @click.stop="showConfirmModal(item, 'queren')">
						确认到达
					</view>
					<view class="qzf" v-if="item.payType === 5" @click.stop="showConfirmModal(item, 'startFu')">
						开始服务
					</view>
				</view>
			</view>
		</view>
		<u-loadmore :status="status" @loadmore="loadMore" />
	</view>
</template>

<script>
export default {
	data() {
		return {
			limit: 10,
			coachId: '',
			shifuId: '',
			tmplIds: [
				'',
				'',
				'',
				'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
			],
			status: 'loadmore',
			list: [
				{ name: '全部', value: 0 },
				{ name: '待上门', value: 3 },
				{ name: '待服务', value: 5 },
				{ name: '服务中', value: 6 },
				{ name: '已完成', value: 7 },
				{ name: '售后', value: 8 },
			],
			currentIndex: 0,
			page: 0,
			orderList: [],
			pay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],
			isLoading: false // Flag to prevent multiple API calls
		};
	},
	onReachBottom() {
		this.loadMore();
	},
	onPullDownRefresh() {
		this.refreshList();
	},
	methods: {
	dingyue() {
		console.log('dingyue called');
		const allTmplIds = this.tmplIds;
		const requiredTmplId = '';
		if (allTmplIds.length < 3) {
			console.error("Not enough template IDs available:", allTmplIds);
			return;
		}
		// Ensure requiredTmplId is included, select 2 more randomly
		const otherTmplIds = allTmplIds.filter(id => id !== requiredTmplId);
		const shuffled = otherTmplIds.sort(() => 0.5 - Math.random());
		const selectedTmplIds = [requiredTmplId, ...shuffled.slice(0, 2)];
		console.log("Selected template IDs:", selectedTmplIds);
		const templateData = selectedTmplIds.map((id, index) => ({
			templateId: id,
			templateCategoryId: index === 0 ? 10 : 5
		}));
		uni.requestSubscribeMessage({
			tmplIds: selectedTmplIds,
			success: (res) => {
				console.log('requestSubscribeMessage result:', res);
				this.templateCategoryIds = [];
				let count = 0;
				selectedTmplIds.forEach((templId, index) => {
					console.log(`Template ${templId} status: ${res[templId]}`);
					if (res[templId] === 'accept') {
						const templateCategoryId = templateData[index].templateCategoryId;
						if (templateCategoryId === 10) {
							for (let i = 0; i < 15; i++) {
								this.templateCategoryIds.push(templateCategoryId);
							}
						} else {
							this.templateCategoryIds.push(templateCategoryId);
						}
						console.log('Accepted message push for template:', templId);
					}
				});
				console.log('Updated templateCategoryIds:', this.templateCategoryIds);
			},
			fail: (err) => {}
		});
	},
		loadMore() {
			if (this.status === 'nomore' || this.isLoading) return;
			this.isLoading = true;
			this.status = 'loading';
			
			const nextPage = this.page + 1;
			this.fetchOrders(nextPage, false);
		},
		
		refreshList() {
			this.page = 0;
			this.orderList = [];
			this.status = 'loadmore';
			this.getList();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		
		fetchOrders(pageNum, replaceList = true) {
			return this.$api.shifu.master_Order({
				coachId: this.shifuId,
				payType: this.currentIndex === 8 ? 7 : this.currentIndex,
				pageNum: pageNum,
				pageSize: this.limit
			}).then(res => {
				if (res.code === '-1') {
					uni.showToast({
						icon: 'none',
						title: res.msg || '没有更多数据'
					});
					this.status = 'nomore';
				} else {
					const list = Array.isArray(res.data.list) ? res.data.list : [];
					const normalizedList = list
						.filter(item => this.currentIndex !== 8 || item.isAftermarket === 1)
						.map(item => ({
							...item,
							payType: parseInt(item.payType)
						}));
					
					if (replaceList) {
						this.orderList = normalizedList;
					} else {
						this.orderList = [...this.orderList, ...normalizedList];
					}
					
					this.page = pageNum;
					this.status = list.length < this.limit ? 'nomore' : 'loadmore';
				}
				this.isLoading = false;
				return res;
			}).catch(err => {
				this.status = 'nomore';
				this.isLoading = false;
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
				console.error('Error loading data:', err);
				return Promise.reject(err);
			});
		},
		
		goDetail(item) {
			uni.setStorageSync('orderdetails', item);
			uni.navigateTo({
				url: `/shifu/master_order_my?id=${item.id}`
			});
		},
		
		goUrl(e) {
			uni.navigateTo({
				url: e
			});
		},
		
		showConfirmModal(item, action) {
			uni.showModal({
				title: action === 'queren' ? '确认到达' : '开始服务',
				content: '请确认操作：' + (action === 'queren' ? '确认到达' : '开始服务'),
				confirmText: '确定',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						if (action === 'queren') {
							this.queren(item);
						} else if (action === 'startFu') {
							this.startFu(item);
						}
					}
				}
			});
		},
		
		async startFu(item) {
			try {
				const res = await this.$api.service.shifuqueren({
					id: item.id,
					payType: 6
				});
				if (res.code === "200") {
					uni.showToast({
						title: '操作成功',
						icon: 'success'
					});
					this.refreshList();
				} else {
					uni.showToast({
						title: res.msg || '操作失败',
						icon: 'none'
					});
				}
			} catch (err) {
				uni.showToast({
					title: '请求失败',
					icon: 'none'
				});
			}
		},
		
		async queren(item) {
			try {
				const res = await this.$api.service.shifuqueren({
					id: item.id,
					payType: 5
				});
				if (res.code === "200") {
					uni.showToast({
						title: '操作成功',
						icon: 'success'
					});
					this.refreshList();
				} else {
					uni.showToast({
						title: res.msg || '操作失败',
						icon: 'none'
					});
				}
			} catch (err) {
				uni.showToast({
					title: '请求失败',
					icon: 'none'
				});
			}
		},
		
		updateHigh(options) {
			const shiInfo = uni.getStorageSync('shiInfo');
			if (!shiInfo) {
				console.log('No shiInfo, skipping updateHighlight');
				return;
			}
			let shiInfoid;
			try {
				shiInfoid = JSON.parse(shiInfo);
			} catch (e) {
				console.error('Error parsing shiInfo:', e);
				return;
			}
			this.$api.service.updataHighlight({
				userId: shiInfoid.id,
				role: 2,
				payType: options.tab
			}).then(res => {
				console.log(res);
			}).catch(err => {
				console.error('Error updating highlight:', err);
			});
		},
		
		getList() {
			if (this.isLoading) return;
			this.isLoading = true;
			this.status = 'loading';
			this.fetchOrders(1, true);
		},
		
		handleHeader(item) {
			if (this.currentIndex === item.value) return;
			this.currentIndex = item.value;
			this.page = 0;
			this.orderList = [];
			this.status = 'loadmore';
			this.getList();
			this.updateHigh({ tab: item.value });
		}
	},
	onLoad(options) {
		let shiInfo = uni.getStorageSync('shiInfo') || '{}';
		try {
			this.shifuId = JSON.parse(shiInfo).id;
		} catch (e) {
			console.error('Error parsing shiInfo:', e);
			this.shifuId = '';
		}
		
		if (options.tab) {
			this.currentIndex = parseInt(options.tab);
		}
		this.updateHigh(options);
		this.getList();
	}
};
</script>

<style scoped lang="scss">
.page {
	background-color: #F8F8F8;
	height: 100vh;
	overflow: auto;
	padding-top: 100rpx;

	.header {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100;
		width: 750rpx;
		height: 100rpx;
		background: #FFFFFF;
		display: flex;
		justify-content: space-around;
		align-items: center;

		.header_item {
			max-width: 85rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #999999;
			display: flex;
			justify-content: center;
			flex-wrap: wrap;

			.blue {
				margin-top: 8rpx;
				width: 38rpx;
				height: 6rpx;
				background: #2E80FE;
				border-radius: 4rpx;
			}
		}
	}

	.main {
		padding:20rpx 30rpx;
		min-height: calc(100vh - 100rpx);

		.main_item {
			width: 690rpx;
			height: 284rpx;
			background: #FFFFFF;
			border-radius: 24rpx;
			padding: 28rpx 36rpx;
			margin-bottom: 20rpx;
			box-sizing: border-box;

			.head {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 24rpx;
				font-weight: 400;
				color: #999999;

				.no {
					max-width: 500rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}

			.mid {
				margin-top: 20rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.lef {
					display: flex;
					align-items: center;

					image {
						width: 120rpx;
						height: 120rpx;
						flex-shrink: 0;
					}

					text {
						font-size: 28rpx;
						font-weight: 400;
						color: #333333;
						margin-left: 30rpx;
						max-width: 350rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}

				.righ {
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;
					text-align: right;
					flex-shrink: 0;
				}
			}

			.bot {
				margin-top: 20rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #999999;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.qzf {
					width: 148rpx;
					height: 48rpx;
					background: #2E80FE;
					border-radius: 50rpx;
					font-size: 20rpx;
					font-weight: 500;
					line-height: 48rpx;
					text-align: center;
					color: #fff;
				}
			}
		}
	}
}
</style>