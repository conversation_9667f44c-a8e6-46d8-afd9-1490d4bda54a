
<template>
	<view class="page">
		<tabbar :cur="0"></tabbar>
		<view class="img">
			<u-swiper :list="list1" height="108"></u-swiper>
		</view>
		<view class="location-bar">
			<view class="location-info">
				<view>当前接单位置：{{ province + city + district || '定位中...' }}</view>
			</view>
		</view>
		<view class="check_box">
			<view class="check">
				<u-tag :text="currentCateName" icon="arrow-down-fill" @click="chooseCate"></u-tag>
				<view class="cate-dropdown" v-if="showCate">
					<view class="cate-item" v-for="(cate, index) in cateList" :key="index" @click="selectClick(cate)">
						{{ cate.name }}
					</view>
				</view>
			</view>
			<view class="reset">
				<u-tag text="重置筛选" @click="reset" plain plain-fill></u-tag>
			</view>
		</view>
		<u-empty mode="order" icon="http://cdn.uviewui.com/uview/empty/order.png" v-if="list.length == 0"></u-empty>
		<view class="re_item" v-for="(item, index) in list" :key="index" @click="seeDetail(item)">
			<view class="top">
				<image :src="item.goodsCover" style="width: 160rpx;height: 160rpx;border-radius: 10rpx;"></image>
				<view class="order">
					<div class="title">{{ item.goodsName }}<span v-if="item.type != 0"
							style="font-size: 24rpx;color:#999;margin-left: 10rpx;">(报价0.00元起)</span></div>
					<div class="price">{{ item.type == 0 ? `￥${item.payPrice}` : '待报价' }}</div>
				</view>
			</view>
			<view @click="dingyue()" class="info">
				<view class="address">
					<view class="left">
						<u-icon name="map-fill" color="#2979ff" size="22"></u-icon>
					</view>
					<view class="right">
						<view class="address_name">{{ item.address }}</view>
						<view class="address_Info">{{ item.addressInfo }}</view>
					</view>
				</view>
				<view class="tel">
					<view class="left">
						<u-icon name="phone-fill" color="#2979ff" size="22"></u-icon>
					</view>
					<view class="right">{{ item.mobile.slice(0, 3) + '********' }}</view>
				</view>
			</view>
			<view class="notes" v-if="item.text != ''">
				<view style="color:#999999;">备注内容:</view>
				{{ item.text }}
			</view>
			<view class="btn" :style="item.type == 1 ? '' : 'background-color:#2E80FE;color:#fff;'"
				@click.stop="seeDetail(item)">
				{{ item.type == 1 ? '立即报价' : '立即接单' }}
			</view>
		</view>
		<u-popup :show="show" :round="10" closeable @close="close">
			<view class="box">
				<view class="title">立即报价</view>
				<view class="title2">报价金额</view>
				<view class="money">
					<u--input placeholder="请输入报价金额" prefixIcon="rmb" prefixIconStyle="font-size: 22px;color: #909399"
						type="digit" v-model="input" @input="validateInput" maxlength="10"></u--input>
				</view>
				<view class="btn" @click="confirmBao">确认报价</view>
			</view>
		</u-popup>
		<u-modal :show="confirmshow" :content="content" showCancelButton @confirm="confirmRe"
			@cancel="confirmshow = false"></u-modal>

		<u-modal :show="masterModalShow" content="您还不是师傅,请去入驻" showCancelButton @confirm="goToSettle"
			@cancel="masterModalShow = false"></u-modal>

		<u-modal :show="detailModalShow" title="服务承诺" showCancelButton cancelText="不同意" confirmText="同意"
			@confirm="confirmDetail" @cancel="detailModalShow = false" v-if="shifustutus.data !== -2 && shifustutus.data !== -1">
			<view class="modal-content">
				<rich-text
					:nodes="getconfigs?getconfigs:configInfo.shifuQualityCommitment"></rich-text>
			</view>
		</u-modal>

		<view class="loadmore" v-if="list.length >= 10">
			<u-loadmore :status="status" />
		</view>
		<!-- <view class="footer">---------------皖ICP备2023012035号-1---------------</view> -->
		</view>
</template>

<script>
	import tabbar from "@/components/tabbarsf.vue";
	import {
		mapState,
		mapActions
	} from 'vuex';
	export default {
		components: {
			tabbar
		},
		data() {
			return {
				orderData: '',
				showDingyue: false,
				showCate: false,
				infodata: '',
			tmplIds: [
				'',
				'',
				'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
			],
				status: 'loadmore',
				id: '',
				shifuId: '',
				list: [],
				show: false,
				confirmshow: false,
				masterModalShow: false,
				detailModalShow: false, // Added for new modal
				content: '确认接下该订单吗',
				input: '',
				area_id: '',
				limit: 50,
				page: 1,
				bannerList: [],
				configInfo: '',
				getconfigs: '',
				list1: [],
				lng: '',
				shifustutus: { // Initialize shifustutus as an object to hold 'data' and 'msg'
					data: 0,
					msg: ''
				},
				lat: '',
				cateList: [],
				currentCateId: '',
				currentCateName: '选择接单分类',
				copyCateList: [],
				province: '',
				city: '',
				district: '',
				isPageLoaded: false, // 添加页面加载状态标识
				selectedItem: null // Added to store the selected item
			};
		},
		computed: {
			...mapState({
				configInfos: (state) => state.config.configInfo,
				refreshReceiving: (state) => state.service.refreshReceiving || '', // Map Vuex state
			})

		},

		methods: {
			dingyue() {
				console.log('dingyue called');
				const allTmplIds = this.tmplIds;
				if (allTmplIds.length < 3) {
					console.error("Not enough template IDs available:", allTmplIds);
					// uni.showToast({
					// 	icon: 'none',
					// 	title: '模板ID不足'
					// });
					return;
				}
				const shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());
				const selectedTmplIds = shuffled.slice(0, 3);
				console.log("Selected template IDs:", selectedTmplIds);
				const templateData = selectedTmplIds.map((id, index) => ({
					templateId: id,
					templateCategoryId: index === 0 ? 10 : 5
				}));
				uni.requestSubscribeMessage({
					tmplIds: selectedTmplIds,
					success: (res) => {
						console.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);
						// Check if any of the template IDs were rejected
						const hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');
						const hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
						if (hasRejection && !hasShownModal) {
							uni.showModal({
								title: '提示',
								content: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收用户订单通知。',
								cancelText: '取消',
								confirmText: '去开启',
								confirmColor: '#007AFF',
								success: (modalRes) => {
									uni.setStorageSync('hasShownSubscriptionModal', true);
									if (modalRes.confirm) {
										uni.openSetting({
											withSubscriptions: true
										});
									} else if (modalRes.cancel) {
										uni.setStorageSync('hasCanceledSubscription', true);
									}
								}
							});
						}
						this.templateCategoryIds = [];
						selectedTmplIds.forEach((templId, index) => {
							console.log(`Template ${templId} status: ${res[templId]}`);
							if (res[templId] === 'accept') {
								const templateCategoryId = templateData[index].templateCategoryId;
								if (templateCategoryId === 10) {
									for (let i = 0; i < 15; i++) {
										this.templateCategoryIds.push(templateCategoryId);
									}
								} else {
									this.templateCategoryIds.push(templateCategoryId);
								}
								console.log('Accepted message push for template:', templId);
							}
						});
						console.log('Updated templateCategoryIds:', this.templateCategoryIds);
					},
					fail: (err) => {
						console.error('requestSubscribeMessage failed:', err);
					}
				});
			},
			

			// 输入验证方法
			validateInput(e) {
				let value = e.detail ? e.detail.value : e;

				// 移除所有中文字符
				value = value.replace(/[\u4e00-\u9fa5]/g, '');

				// 只允许数字和小数点
				value = value.replace(/[^\d.]/g, '');

				// 处理小数点逻辑
				const parts = value.split('.');
				if (parts.length > 2) {
					// 如果有多个小数点，只保留第一个
					value = parts[0] + '.' + parts.slice(1).join('');
				}

				if (parts.length === 2) {
					// 如果有小数部分，限制小数点后只能有两位
					if (parts[1].length > 2) {
						parts[1] = parts[1].substring(0, 2);
						value = parts[0] + '.' + parts[1];
					}
				}

				// 防止以多个0开头（除了0.开头的情况）
				if (value.length > 1 && value.charAt(0) === '0' && value.charAt(1) !== '.') {
					value = value.substring(1);
				}

				// 更新input值
				this.input = value;
			},

			reset() {
				this.currentCateName = '选择接单分类';
				this.currentCateId = '';
				this.page = 1;
				this.getList();
			},
			textsss() {
				if (this.infodata.status === 2) {
					uni.requestSubscribeMessage({
						tmplIds: this.tmplIds,
						success: (res) => {
							console.log('requestSubscribeMessage result:', res);
						},
						fail: (err) => {
							console.log('requestSubscribeMessage failed:', err);
						}
					});
				}
			},
			closeCate() {
				this.showCate = false;
				setTimeout(() => {
					this.cateList = this.copyCateList;
				}, 500);
			},
			chooseCate() {
				this.showCate = !this.showCate;
			},
			asyncChange() {
				uni.showModal({
					title: '订阅提示',
					content: '请手动开启消息订阅以获取订单状态提醒',
					success: (res) => {
						uni.navigateTo({
							url: '/shifu/userProfile'
						})
					},
					fail: (err) => {
						// console.log('uni.showModal failed:', err);
						// uni.showToast({
						// 	title: '显示订阅提示失败',
						// 	icon: 'none'
						// });
					}
				});
			},
			confirmSubscription() {
				uni.openSetting({
					withSubscriptions: true,
					success: () => {
						console.log('uni.openSetting success');
						uni.getSetting({
							withSubscriptions: true,
							success: (res) => {
								const subscriptions = res.subscriptionsSetting;
								console.log('uni.getSetting result:', subscriptions);
								if (subscriptions.mainSwitch) {
									this.requestSubscription();
								} else {
									this.showDingyue = true;
									uni.showToast({
										title: '订阅取消',
										icon: 'none'
									});
									this.checkSubscriptionStatus();
								}
							},
							fail: (err) => {
								console.log('uni.getSetting failed:', err);
								this.showDingyue = true;
								uni.showToast({
									title: '获取设置失败',
									icon: 'none'
								});
							}
						});
					},
					fail: (err) => {
						console.log('uni.openSetting failed:', err);
						this.showDingyue = true;
						uni.showToast({
							title: '打开设置失败，请稍后重试',
							icon: 'none'
						});
					}
				});
			},
			cancelSubscription() {
				this.checkSubscriptionStatus();
				uni.showToast({
					title: '订阅取消',
					icon: 'none'
				});
			},
			requestSubscription() {
				let infodata = JSON.parse(uni.getStorageSync('shiInfo'));
				console.log(infodata);
				if (infodata.status === 2) {
					uni.requestSubscribeMessage({
						tmplIds: this.tmplIds,
						success: (res) => {
							console.log('requestSubscribeMessage result:', res);
							const anyAccepted = this.tmplIds.some(id => res[id] === 'accept');
							if (anyAccepted) {
								this.showDingyue = false;
								uni.showToast({
									title: '消息订阅成功',
									icon: 'success'
								});
							} else {
								this.showDingyue = true;
								uni.showToast({
									title: '订阅取消',
									icon: 'none'
								});
							}
							this.checkSubscriptionStatus();
						},
						fail: (err) => {
							console.log('requestSubscribeMessage failed:', err);
							this.showDingyue = true;
							this.checkSubscriptionStatus();
						}
					});
				}
			},
			checkSubscriptionStatus() {
				uni.getSetting({
					withSubscriptions: true,
					success: (res) => {
						const subscriptions = res.subscriptionsSetting;
						if (subscriptions.mainSwitch) {
							const anySubscribed = this.tmplIds.some(id =>
								subscriptions.itemSettings && subscriptions.itemSettings[id] === 'accept'
							);
							this.showDingyue = !anySubscribed;
						} else {
							this.showDingyue = true;
						}
						console.log('Updated showDingyue:', this.showDingyue);
					},
					fail: (err) => {
						console.log('checkSubscriptionStatus failed:', err);
						this.showDingyue = true;
						uni.showToast({
							title: '检查订阅状态失败',
							icon: 'none'
						});
					}
				});
			},
			async selectClick(cate) {
				if (cate.id) {
					try {
						const res = await this.$api.shifu.indexQuote({
							lng: this.lng,
							lat: this.lat,
							parentId: cate.id
						});
						if (res.code === "-1") {
							uni.showToast({
								icon: 'none',
								title: res.msg
							}, 3000);
						}
						this.showCate = false;
						this.currentCateName = cate.name;
						this.currentCateId = cate.id;
						this.list = res.data.list || [];

						let count = this.list.length;
						console.log('List count:', count);
						uni.setStorageSync('listCount', count);
						this.$forceUpdate();
					} catch (error) {
						// uni.showToast({
						// 	icon: 'none',
						// 	title: '获取分类订单失败'
						// });
					}
				}
			},
			async getCate() {
				try {
					const res = await this.$api.shifu.serviceCate();
					this.cateList = res || [];
					this.copyCateList = res || [];
				} catch (error) {
					uni.showToast({
						icon: 'none',
						title: '获取分类失败'
					});
				}
			},
			async seeDetail(item) {
				this.$api.shifu.getshifstutas().then(res => {
					console.log("shifustutus response:", res);
					this.shifustutus = res; // Assign the whole response object
				});
				try {
					this.selectedItem = item; // Store the selected item
					if (this.shifustutus.data === -2) {
						// If shifustutus.data is -2, show the "成为师傅才能操作" modal
						this.masterModalShow = true;
					} else if (this.shifustutus.data === -1) {
						// If shifustutus.data is -1, show the message from shifustutus.msg
						uni.showToast({
							icon: 'none',
							title: this.shifustutus.msg || '无法进行此操作'
						});
						return;
					} else {
						// For other shifustutus.data values, show the "服务承诺" modal
						this.detailModalShow = true;
					}
				} catch (error) {
					uni.showToast({
						icon: 'none',
						title: '检查身份失败'
					});
				}
			},
			confirmDetail() {
				if (this.selectedItem) {
					uni.setStorageSync('selectedOrder', this.selectedItem);
					uni.navigateTo({
						url: `/shifu/master_order_details?id=${this.selectedItem.id}`
					});
				}
				this.detailModalShow = false;
				this.selectedItem = null;
			},
			async getList() {
				uni.showLoading({
					title: '加载中'
				});
				try {
					let location = {
						longitude: '',
						latitude: ''
					};
					try {
						await new Promise(resolve => setTimeout(resolve, 500));
						location = await new Promise((resolve, reject) => {
							uni.getLocation({
								type: 'gcj02',
								success: resolve,
								fail: reject
							});
						});
						this.lng = location.longitude;
						this.lat = location.latitude;
					} catch (error) {
						this.lng = '115.259956';
						this.lat = '33.066271';
						uni.showToast({
							icon: 'none',
							title: '定位失败，使用默认位置'
						});
					}

					try {
						const geoRes = await new Promise((resolve, reject) => {
							uni.request({
								url: `https://restapi.amap.com/v3/geocode/regeo?location=${this.lng},${this.lat}&key=2fb9ec1a184338e3cce567b7d2bab08f`,
								success: resolve,
								fail: reject
							});
						});
						this.province = geoRes.data.regeocode.addressComponent.province || '';
						this.city = geoRes.data.regeocode.addressComponent.city || '';
						this.district = geoRes.data.regeocode.addressComponent.district || '';
					} catch (error) {
						this.province = '安徽省';
						this.city = '阜阳市';
						this.district = '临泉县';
						uni.showToast({
							icon: 'none',
							title: '地址解析失败，使用默认地址'
						});
					}

					const res = await this.$api.shifu.indexQuote({
						lng: this.lng,
						lat: this.lat,
						parentId: 0,
						pageNum: this.page,
						pageSize: this.limit,
					});
					console.log(res)
					if (res.code === "-1") {
						uni.showToast({
							icon: 'none',
							title: res.msg
						}, 3000);
					}
					this.$set(this, 'list', res.data.list || []);
					let count = this.list.length;
					console.log('List count:', count);
					uni.setStorageSync('listCount', count);
					this.$forceUpdate();
				} catch (error) {
					console.log(error)
					// uni.showToast({
					// 	icon: 'none',
					// 	title: '获取订单列表失败'
					// });
					this.$set(this, 'list', []);
				} finally {
					uni.hideLoading();
				}
			},
			handleReceive(item) {
				this.textsss();
				this.orderData = item;
				this.id = item.id;
				if (item.type == 0) {
					this.confirmshow = true;
				} else {
					this.show = true;
				}
			},
			close() {
				this.show = false;
				this.input = '';
			},
			confirmRe() {
				this.confirmshow = false;
				this.$api.shifu.rece_Order({
					order_id: this.id
				}).then(res => {
					this.getList();
					uni.showToast({
						icon: 'success',
						title: '接单成功',
						duration: 1000
					});
					setTimeout(() => {
						uni.navigateTo({
							url: '/shifu/master_my_order'
						});
					}, 1000);
				}).catch(error => {
					uni.showToast({
						icon: 'fail',
						title: error.message || '接单失败'
					});
				});
			},
			goToSettle() {
				this.masterModalShow = false;
				uni.navigateTo({
					url: '/shifu/Settle'
				});
			},
			async getServiceInfo() {
				try {
					const res = await this.$api.shifu.index({
						city_id: this.area_id
					});
					this.bannerList = res.data || [];
					this.list1 = res.data.map(item => item.img) || [];
					if (!this.list1.length) {
						this.list1 = [
							'https://zskj.asia/attachment/image/666/24/09/2bdd13fab41b42b987bcfc501aa535bb.jpg'
						];
					}
				} catch (error) {
					uni.showToast({
						icon: 'none',
						title: '获取轮播图失败'
					});
					this.list1 = ['https://zskj.asia/attachment/image/666/24/09/e790eea3f21b4f48ab2b00b034468035.jpg'];
				}
			},
			onReachBottom() {
				if (this.status == 'nomore') return;
				this.status = 'loading';
				setTimeout(() => {
					this.page++;
					this.$api.shifu.indexQuote({
						pageNum: this.page,
						pageSize: 20,
						parentId: this.currentCateId || 0,
						lat: this.lat,
						lng: this.lng,
					}).then(res => {
						if (!res.data || !res.data.list || res.data.list.length === 0) {
							this.status = 'nomore';
							uni.showToast({
								icon: 'none',
								title: '没有更多数据'
							});
							return;
						}
						this.$set(this, 'list', [...this.list, ...(res.data.list || [])]);
						if (res.data.list.length < 10) {
							this.status = 'nomore';
						} else {
							this.status = 'loadmore';
						}
					}).catch(error => {
						this.status = 'nomore';
						uni.showToast({
							icon: 'none',
							title: '加载失败，请稍后重试'
						});
					});
				}, 1000);
			},
			confirmBao() {
				this.textsss();
				if (this.input == '' || this.input == 0) {
					uni.showToast({
						icon: 'none',
						title: '请输入报价（不能为0哦）'
					});
					return;
				}
				const updatedOrderData = {
					orderId: this.id,
					price: this.input
				};
				this.$api.shifu.updateBao(updatedOrderData).then(res => {
					if (res === -1) {
						this.masterModalShow = true;
					} else {
						uni.showToast({
							icon: 'success',
							title: '报价成功'
						});
						this.getList();
						this.close();
						setTimeout(() => {
							uni.redirectTo({
								url: '/shifu/master_bao_list'
							});
						}, 1000);
						this.getList();
					}
				}).catch(error => {
					uni.showToast({
						icon: 'fail',
						title: error.message || '报价失败'
					});
					this.close();
				});
			},
			async initializePage() {
				uni.showLoading({
					title: '初始化中'
				});
				try {
					const systemInfo = uni.getSystemInfoSync();
					console.log('Platform:', systemInfo.platform);
					await this.getServiceInfo();
					await this.getCate();
					await this.getList();
					await this.checkSubscriptionStatus();
					this.$forceUpdate();
				} catch (error) {
					uni.showToast({
						icon: 'none',
						title: '初始化失败，请稍后重试'
					});
				} finally {
					uni.hideLoading();
				}
			}
		},
		async onLoad() {

			this.$api.base.getConfig().then(res => {
				// console.log(res)
				this.getconfigs = res.shifuQualityCommitment
				// console.log(this.getconfigs)
			})
			// Modified assignment for shifustutus
			this.$api.shifu.getshifstutas().then(res => {
				console.log("shifustutus response:", res);
				this.shifustutus = res; // Assign the whole response object
			});
			if (uni.getStorageSync('shiInfo')) {
				this.infodata = JSON.parse(uni.getStorageSync('shiInfo'));
			}
			this.configInfo = uni.getStorageSync('configInfo');
			console.log(this.infodata);
			this.isPageLoaded = true; // 标记页面已初始化
			await this.initializePage();

		},
		async onPullDownRefresh() {
			this.page = 1;
			this.list = [];
			await this.getList();
			uni.stopPullDownRefresh();
		},
		async onShow() {

			this.checkSubscriptionStatus();
			uni.$on('refreshReceivingList', () => {
				console.log('接收到刷新通知，正在重新加载订单列表...');
				// 这里可以加上一些交互提示，比如显示一个 loading
				// 然后立即调用获取数据的方法
				this.page = 1;
				this.getList();
			});

		}
	};
</script>

<style scoped lang="scss">
	.page {
		min-height: 100vh;
		overflow: auto;
		background-color: #f3f4f5;
		padding-bottom: 120rpx;

		.img {
			width: 690rpx;
			margin: 20rpx auto;
		}

		.location-bar {
			display: flex;
			padding: 20rpx;
			border: 1rpx solid #eeeeee;
			color: #999;
			font-size: 28rpx;
		}

		.subscription {
			flex-shrink: 0;
		}

		.location-info {
			margin-left: auto;
			text-align: right;
		}

		.check_box {
			margin: 20rpx auto;
			width: 690rpx;
			display: flex;
			justify-content: space-between;
			position: relative;

			.check {
				width: 500rpx;
				position: relative;
			}

			.reset {
				width: 160rpx;
			}

			.cate-dropdown {
				position: absolute;
				top: 80rpx;
				left: 0;
				width: 500rpx;
				max-height: 400rpx;
				overflow-y: auto;
				background-color: #fff;
				border-radius: 10rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
				z-index: 1000;
			}

			.cate-item {
				padding: 20rpx 30rpx;
				font-size: 28rpx;
				color: #333;
				border-bottom: 1rpx solid #f0f0f0;

				&:last-child {
					border-bottom: none;
				}

				&:hover {
					background-color: #f8f8f8;
				}
			}
		}

		.box {
			padding: 40rpx 30rpx;

			.title {
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #171717;
			}

			.title2 {
				margin-top: 32rpx;
				margin-bottom: 20rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #171717;
			}

			.btn {
				margin: 0 auto;
				margin-top: 42rpx;
				width: 688rpx;
				height: 98rpx;
				background: #2E80FE;
				border-radius: 12rpx;
				line-height: 98rpx;
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #FFFFFF;
			}
		}

		.modal-content {
			padding: 20rpx;
			max-height: 400rpx;
			overflow-y: auto;
		}

		.re_item {
			width: 690rpx;
			background-color: #fff;
			margin: 20rpx auto;
			padding: 40rpx;

			.top {
				display: flex;

				image {
					margin-right: 20rpx;
				}

				.order {
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.title {
						font-size: 28rpx;
						font-weight: 500;
						color: #171717;
						max-width: 500rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.price {
						font-size: 28rpx;
						font-weight: 500;
						color: #2E80FE;
					}
				}
			}

			.info {
				margin-top: 40rpx;

				.address {
					display: flex;
					align-items: center;

					.left {
						margin-right: 20rpx;
					}

					.right {
						.address_name {
							font-size: 40rpx;
							font-weight: 500;
							color: #333333;
							max-width: 500rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}

						.address_info {
							margin-top: 12rpx;
							font-size: 28rpx;
							font-weight: 400;
							color: #333333;
							max-width: 500rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}
				}

				.tel {
					margin-top: 20rpx;
					display: flex;
					align-items: center;

					.left {
						margin-right: 20rpx;
					}

					.right {
						font-size: 40rpx;
						font-weight: 500;
						color: #333333;
						max-width: 500rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}
			}

			.notes {
				background-color: #f2f3f4;
				border-radius: 5rpx;
				padding: 10rpx;
			}

			.btn {
				margin: 0 auto;
				margin-top: 40rpx;
				width: 610rpx;
				height: 82rpx;
				border-radius: 12rpx;
				border: 2rpx solid #2E80FE;
				line-height: 82rpx;
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #2E80FE;
			}
		}

		.loadmore {
			display: flex;
			justify-content: center;
		}

		.footer {
			color: #333;
			margin: 20rpx 0;
			text-align: center;
			font-size: 24rpx;
		}
	}
</style>
```