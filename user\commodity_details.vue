<template>
  <view class="page" v-if="ready">
    <u-modal :show="showChoose" :content="content"></u-modal>
    <view class="choose_box" :style="showChoose ? '' : 'height:0'">
      <view class="head">选择下单模式</view>
      <view class="close" @tap="showChoose = false">
        <image src="/static/images/9397.png" mode=""></image>
      </view>
	  	<view class="choose_item" @click="confirmType(0)" v-if="serviceDet.service_price_type != 1">
	  				<image src="../static/images/9468.png" mode=""></image>
	  				<view class="title">
	  					一口价模式
	  				</view>
	  				<view class="ctx">平台推出的尊享模式，提供一口价服务模式，师傅实时接单，给您提供最优质的的服务</view>
	  			</view>
      <view class="choose_item" @tap="confirmType(1)" v-if="serviceDet.service_price_type != 0">
        <image src="/static/images/9469.png" mode=""></image>
        <view class="title">比价模式</view>
        <view class="ctx">订单发布后师傅进行报价，您可以实时比价，并选择最优师傅进行服务</view>
      </view>
    </view>
    <view class="header">
      <image :src="serviceDet.cover" mode=""></image>
      <view class="Info">
        <view class="title">
          {{ serviceDet.title }}
          <span
            v-if="serviceDet.service_price_type != 0"
            style="margin-left: 10rpx; font-size: 26rpx; color: #999;"
          >
            (报价{{ serviceDet.initPrice }}元起)
          </span>
        </view>
        <view class="price" v-if="serviceDet.service_price_type != 1">一口价      ￥{{ serviceDet.price }}元</view>
        <view class="num">已服务{{ serviceDet.totalSale }}次</view>
      </view>
    </view>
    <view class="fg"></view>
    <view class="site">
      <view class="top">
        <view class="left">附近服务点</view>
        <view
          class="right"
          @tap="goUrl(`../user/agent?id=${id}`)"
          v-if="hasValidAgents"
        >
          查看更多
        </view>
        <view v-else>
          暂无服务点
        </view>
      </view>
      <view v-if="serviceDet.agents && serviceDet.agents.length > 0 && hasValidAgents" class="site_box">
        <view
          class="site_item"
          v-for="(item, index) in (serviceDet.agents.length > 2 ? serviceDet.agents.slice(0, 2) : serviceDet.agents)"
          :key="index"
        >
          <image :src="item.img" mode="scaleToFill" style="border-radius: 10rpx;" @tap="seeImg(item.img)"></image>
          <view class="content">
            <view class="name">{{ item.serviceCate }}</view>
            <view style="display: flex; justify-content: flex-end;">
              <u-icon name="attach" size="28" @tap="seeImg(item.sunCode)"></u-icon>
            </view>
            <view class="address">
              <view class="position" @tap="copyAddress(item.address)">
                <text>{{ item.name }} | {{ item.address }}</text>
                <u-icon name="arrow-right" color="#333" size="10"></u-icon>
              </view>
              <u-icon
                name="phone-fill"
                color="#599eff"
                size="28"
                @tap="phoneDLD(item.tel)"
              ></u-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="eva">
      <view class="top">
        <view class="left">服务评价（{{ long }}条）</view>
        <view class="right" @tap="moreEva">查看更多</view>
      </view>
      <view class="eva_item" v-for="(item, index) in evalist" :key="index">
        <view style="display: flex; justify-content: space-between; align-items: center;" class="">
          <view style="margin-top: 20rpx;" class="">
            <view class="top">
              <image :src="item.avatarUrl || '/static/mine/default_user.png'" mode="" @tap="seeImg(item.avatarUrl || '/static/mine/default_user.png')"></image>
              <view class="name">{{ item.nickName || '' }}</view>
            </view>
            <view class="ctx">{{ item.text }}</view>
            <view class="eva_images" v-if="item.imgs && item.imgList && item.imgList.length > 0">
              <image
                v-for="(img, imgIndex) in item.imgList"
                :key="imgIndex"
                :src="img"
                style="width: 120rpx; height: 120rpx; margin-right: 20rpx;"
                @tap="previewEvaImage(img, item.imgList)"
              ></image>
            </view>
          </view>
          <view class="">
            <view class=""><u-rate readonly count="5" v-model="item.star"></u-rate></view>
          </view>
        </view>
      </view>
    </view>
    <view class="details">
      <u-divider text="详情" textColor="#333"></u-divider>
      <view class="">
        <rich-text alt="" class="ddd" :nodes="serviceDet.introduce"></rich-text>
      </view>
    </view>
    <view class="fg"></view>
    <view style="padding: 30rpx;" class="">
      <rich-text :nodes="getconfigs.userNotice"></rich-text>
    </view>
    <view class="footer">
      <view class="righ" @tap="showChoose = true">立即下单</view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import $store from '@/store/index.js';

export default {
  data() {
    return {
      ready: false,
      showChoose: false,
      content: '',
      id: '',
      getconfigs: '',
      serviceId: '',
      lng: 115.277,
      lat: 33.038799,
      serviceDet: {
        agents: []
      },
      evalist: [],
      tmplIds: [
        '',
        '',
        '',
        'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
      ],
      num: 2,
      fuwuinfo: [],
      configInfos: '',
      long: 0,
    };
  },
  computed: {
    hasValidAgents() {
      if (!this.serviceDet.agents || this.serviceDet.agents.length === 0) {
        return false;
      }
      return this.serviceDet.agents.some(agent => 
        agent && (agent.name || agent.serviceCate || agent.address || agent.img)
      );
    },
    ...mapState({
      primaryColor: (state) => state.config.configInfo.primaryColor,
      subColor: (state) => state.config.configInfo.subColor,
      configInfo: (state) => state.config.configInfo,
      commonOptions: (state) => state.user.commonOptions,
      userInfo: (state) => state.user.userInfo,
      userPageType: (state) => state.user.userPageType,
      mineInfo: (state) => state.user.mineInfo,
    })
  },
  methods: {
    seeImg(url) {
      if (url) {
        uni.previewImage({
          urls: [url],
          current: url
        });
      }
    },
    phoneDLD(info) {
      uni.makePhoneCall({
        phoneNumber: info,
      });
    },
    copyAddress(address) {
      uni.setClipboardData({
        data: address,
        success: () => {
          uni.showToast({
            title: '地址已复制',
            icon: 'success'
          });
        }
      });
    },
    moreEva() {
      if (this.num + 1 > this.long) {
        uni.showToast({
          icon: 'none',
          title: '没有更多了',
        });
        return;
      }
      this.num += 1;
      this.getevalist();
    },
    getevalist() {
      this.$api.service.getServiceCate(this.id).then((res) => {
        console.log('evalist response:', res);
        this.long = res.data.list.length;
        // 处理评价数据
        res.data.list.forEach((item) => {
          // 处理评价图片
          if (item.imgs && typeof item.imgs === 'string') {
            item.imgList = item.imgs.split(',').filter(img => img.trim() !== '');
          } else {
            item.imgList = [];
          }
          
          // 确保avatarUrl是字符串格式
          if (item.avatarUrl && Array.isArray(item.avatarUrl)) {
            item.avatarUrl = item.avatarUrl[0] || '';
          } else if (typeof item.avatarUrl !== 'string') {
            item.avatarUrl = '';
          }
        });
        this.evalist = res.data.list.slice(0, this.num);
      });
    },
    getfuwu() {
      this.$api.service.getagents({
        cityName: "阜阳市",
        latitude: this.lat,
        longitude: this.lng,
        serviceId: this.id,
        pageNum: 1,
        pageSize: 5
      }).then((ress) => {
		  let res=ress.data
        console.log('fuwu response:', res);
        this.serviceDet.agents = res.list && res.list.length > 0 ? res.list : [];
        this.fuwuinfo = res.list || [];
      });
    },
    goUrl(e) {
      uni.navigateTo({
        url: e,
      });
    },
    confirmType(e) {
      let token = uni.getStorageSync('token');
      console.log(token);
      if (!token) {
        uni.showModal({
          title: '提示',
          content: '请先登录',
          success: (res) => {
            if (res.confirm) {
              uni.redirectTo({
                url: '/pages/mine',
              });
            }
          },
        });
        return;
      }
      this.showChoose = false;
   		if (e == 0) {
   					$store.commit('changeType', 0)
   					uni.navigateTo({
   						url: `/user/price_parity?id=${this.serviceDet.id}&type=${0}`
   					})
   				} else if (e == 1) {
   					$store.commit('changeType', 1)
   					uni.navigateTo({
   						url: `/user/price_parity?id=${this.serviceDet.id}&type=${1}`
   					})
   				}
    },
    processRichTextImages(html) {
      if (!html) return html;
      return html.replace(/<img[^>]+>/gi, (match) => {
        let processed = match.replace(/\s(width|height)="[^"]*"/gi, '');
        if (!processed.includes('style=')) {
          processed = processed.replace('<img', '<img style="max-width:100%;height:auto;"');
        } else {
          processed = processed.replace('style="', 'style="max-width:100%;height:auto;');
        }
        return processed;
      });
    },
    previewEvaImage(url, urls) {
      if (url && urls && urls.length > 0) {
        uni.previewImage({
          urls: urls,
          current: url,
        });
      }
    },
    async getDetails(id) {
      await this.$api.service.getserviceInfo(id).then((res) => {
        console.log('service details:', res);
        this.serviceDet = { 
          ...this.serviceDet, 
          ...res.data
        };
        if (this.serviceDet.introduce) {
          this.serviceDet.introduce = this.processRichTextImages(this.serviceDet.introduce);
        }
      });
    },
  },
  async onLoad(options) {
    console.log('userInfo:', this.$store.state.user.userInfo);
    console.log('onLoad options:', options);
    this.id = options.id;
    this.$api.base.getConfig().then(res => {
      this.getconfigs = res;
    });
    this.configInfos = uni.getStorageSync('configInfo');
    this.lat = uni.getStorageSync('lat');
    this.lng = uni.getStorageSync('lng');
    await this.getDetails(this.id);
    await this.getevalist();
    await this.getfuwu();
    this.ready = true;
  },
  onShow() {},
};
</script>

<style scoped lang="scss">
.page {
  padding-bottom: 166rpx;

  ::v-deep .u-popup__content {
    display: none;
  }

  .fg {
    height: 20rpx;
    background: #f0f0f0;
  }

  .choose_box {
    width: 750rpx;
    height: 692rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    position: fixed;
    bottom: 0;
    z-index: 10076;
    transition: all 0.5s;

    .head {
      margin-top: 40rpx;
      text-align: center;
      font-size: 32rpx;
      font-weight: 500;
      color: #171717;
    }

    .close {
      position: absolute;
      top: 44rpx;
      right: 32rpx;

      image {
        width: 37rpx;
        height: 37rpx;
      }
    }

    .choose_item {
      width: 686rpx;
      height: 200rpx;
      position: relative;
      margin: 0 auto;
      margin-top: 40rpx;

      image {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: -1;
      }

      .title {
        padding-top: 40rpx;
        padding-left: 40rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #824109;
      }

      .ctx {
        max-width: 524rpx;
        margin-top: 16rpx;
        padding-left: 40rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: #a38071;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
      }
    }
  }

  .header {
    image {
      width: 750rpx;
      height: 620rpx;
    }

    .Info {
      padding: 40rpx 32rpx;

      .title {
        max-width: 550rpx;
        font-size: 40rpx;
        font-weight: 500;
        color: #171717;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .price {
        margin-top: 12rpx;
        font-size: 30rpx;
        font-weight: 500;
        color: #e72427;
      }

      .num {
        margin-top: 12rpx;
        font-size: 24rpx;
        fontgroups-weight: 400;
        color: #999999;
      }
    }
  }

  .site {
    padding: 40rpx 32rpx;

    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        font-size: 32rpx;
        font-weight: 500;
        color: #171717;
      }

      .right {
        font-size: 28rpx;
        font-weight: 400;
        color: #2e80fe;
      }
    }

    .site_box {
      .site_item {
        margin-top: 40rpx;
        display: flex;
        border-bottom: 2rpx solid #e9e9e9;

        image {
          width: 202rpx;
          height: 142rpx;
          margin-right: 20rpx;
        }

        .content {
          flex: 1;

          .name {
            width: 464rpx;
            min-height: 76rpx;
            font-size: 28rpx;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            white-space: normal;
          }

          .address {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .position {
              display: flex;
              align-items: center;

              text {
                font-size: 24rpx;
                color: #333333;
                max-width: 320rpx;
                white-space: normal;
                margin-right: 10rpx;
              }
            }
          }
        }
      }
    }
  }

  .details {
    padding: 20rpx 30rpx;

    .ddd {
      width: 100%;
      overflow: hidden;

      img {
        max-width: 100% !important;
        height: auto !important;
        display: block;
      }
    }
  }

  .eva {
    padding: 20rpx 30rpx;

    .top {
      padding-bottom: 20rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 2rpx solid #dddddd;

      .left {
        font-size: 28rpx;
        font-weight: 400;
        color: #3b3b3b;
      }

      .right {
        font-size: 28rpx;
        font-weight: 400;
        color: #999999;
      }
    }

    .eva_item {
      padding: 20rpx 0;
      border-bottom: 2rpx solid #dddddd;

      .top {
        display: flex;
        align-items: center;
        border: none;
        padding-bottom: 0;

        image {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 12rpx;
          cursor: pointer;
        }
      }

      .ctx {
        margin-top: 18rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #999999;
      }

      .eva_images {
        margin-top: 18rpx;
        display: flex;
        flex-wrap: wrap;
        
        image {
          cursor: pointer;
          border-radius: 8rpx;
        }
      }
    }
  }

  .footer {
    padding: 38rpx 32rpx;
    width: 750rpx;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
    position: fixed;
    bottom: 0;
    display: flex;
    align-items: center;

    .righ {
      width: 690rpx;
      height: 88rpx;
      background: #2e80fe;
      border-radius: 44rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #ffffff;
      line-height: 88rpx;
      text-align: center;
    }
  }
}
</style>