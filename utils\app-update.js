import $api from '@/api/index.js'

/**
 * APP热更新工具类
 * 支持强制更新和静默更新
 */
class AppUpdate {
  constructor() {
    this.isChecking = false // 防止重复检查
    this.currentVersion = '' // 当前版本号
    this.platform = 2 // 平台类型：1-师傅端，2-用户端
  }

  /**
   * 清除版本缓存
   */
  clearVersionCache() {
    this.currentVersion = null
    // 清除本地存储的版本信息
    try {
      uni.removeStorageSync('app_current_version')
      uni.removeStorageSync('app_last_check_time')
      uni.removeStorageSync('app_last_update_version') // 清除上次更新的版本记录
      console.log('版本缓存已清除')
    } catch (error) {
      console.error('清除版本缓存失败:', error)
    }
  }

  /**
   * 比较版本号
   * @param {string} version1 版本1
   * @param {string} version2 版本2
   * @returns {number} 1: version1 > version2, 0: 相等, -1: version1 < version2
   */
  compareVersion(version1, version2) {
    if (!version1 || !version2) return 0

    const v1Parts = version1.split('.').map(Number)
    const v2Parts = version2.split('.').map(Number)
    const maxLength = Math.max(v1Parts.length, v2Parts.length)

    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0
      const v2Part = v2Parts[i] || 0

      if (v1Part > v2Part) return 1
      if (v1Part < v2Part) return -1
    }

    return 0
  }

  /**
   * 获取当前APP版本信息
   * 优先获取wgt包版本，如果没有则获取原生APP版本
   */
  getCurrentVersion() {
    // #ifdef APP-PLUS
    try {
      const systemInfo = uni.getSystemInfoSync()
      console.log('系统版本信息:', systemInfo)

      // 尝试获取wgt包版本（热更新后的版本）
      return new Promise((resolve) => {
        plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
          console.log('Widget信息:', widgetInfo)

          // 优先使用wgt版本，如果没有则使用原生APP版本
          const wgtVersion = widgetInfo.version
          const appVersion = systemInfo.appVersion || plus.runtime.version

          // 如果wgt版本存在且不同于原生版本，说明有热更新
          if (wgtVersion && wgtVersion !== appVersion) {
            console.log('检测到热更新版本:', wgtVersion, '原生版本:', appVersion)
            this.currentVersion = wgtVersion
            resolve(wgtVersion)
          } else {
            console.log('使用原生APP版本:', appVersion)
            this.currentVersion = appVersion
            resolve(appVersion)
          }
        })
      })
    } catch (error) {
      console.error('获取版本信息失败:', error)
      this.currentVersion = '1.0.0'
      return Promise.resolve('1.0.0')
    }
    // #endif

    // #ifndef APP-PLUS
    // 非APP环境返回默认版本
    this.currentVersion = '1.0.0'
    return Promise.resolve('1.0.0')
    // #endif
  }

  /**
   * 检查版本更新
   * @param {Object} options 配置选项
   * @param {Boolean} options.silent 是否静默检查（不显示"已是最新版本"提示）
   * @param {Boolean} options.showLoading 是否显示加载提示
   */
  async checkUpdate(options = {}) {
    const { silent = false, showLoading = true } = options

    // 防止重复检查
    if (this.isChecking) {
      console.log('正在检查更新中...')
      return
    }

    // #ifndef APP-PLUS
    if (!silent) {
      uni.showToast({
        title: '仅支持APP版本更新',
        icon: 'none'
      })
    }
    return
    // #endif

    // #ifdef APP-PLUS
    this.isChecking = true

    try {
      if (showLoading) {
        uni.showLoading({
          title: '检查更新中...'
        })
      }

      // 获取当前版本
      const currentVersion = await this.getCurrentVersion()
      console.log('检查更新 - 当前版本:', currentVersion)

      // 调用后端接口检查更新
      const response = await $api.user.checkAppVersion({
        version: currentVersion,
        platform: this.platform
      })

      console.log('检查更新 - 服务器响应:', response)

      if (showLoading) {
        uni.hideLoading()
      }

      if (response.code === '200' && response.data) {
        const updateInfo = response.data
        console.log('检查更新 - 更新信息:', updateInfo)

        if (updateInfo.needUpdate) {
          // 需要更新
          console.log('检查更新 - 发现新版本，显示更新对话框')
          this.showUpdateDialog(updateInfo)
          return updateInfo // 返回更新信息
        } else {
          // 已是最新版本
          console.log('检查更新 - 已是最新版本')
          if (!silent) {
            uni.showToast({
              title: '已是最新版本',
              icon: 'success'
            })
          }
          return null
        }
      } else {
        throw new Error(response.msg || '检查更新失败')
      }
    } catch (error) {
      console.error('检查更新失败:', error)
      if (showLoading) {
        uni.hideLoading()
      }
      if (!silent) {
        uni.showToast({
          title: '检查更新失败',
          icon: 'none'
        })
      }
      throw error // 重新抛出错误，便于调用方处理
    } finally {
      this.isChecking = false
    }
    // #endif
  }

  /**
   * 显示更新对话框
   * @param {Object} updateInfo 更新信息
   */
  showUpdateDialog(updateInfo) {
    console.log('显示更新对话框 - 更新信息:', updateInfo)

    const {
      latestVersion,
      description,
      wgtUrl,
      forceUpdate
    } = updateInfo

    const title = `发现新版本 v${latestVersion}`
    const content = description || '建议您立即更新以获得更好的使用体验'

    console.log('显示更新对话框 - 标题:', title)
    console.log('显示更新对话框 - 内容:', content)
    console.log('显示更新对话框 - 是否强制更新:', forceUpdate)

    uni.showModal({
      title,
      content,
      showCancel: !forceUpdate, // 强制更新时不显示取消按钮
      confirmText: '立即更新',
      cancelText: '稍后再说',
      success: (res) => {
        console.log('更新对话框用户选择:', res)
        if (res.confirm) {
          console.log('用户选择立即更新')
          this.downloadAndInstall(updateInfo)
        } else if (forceUpdate) {
          // 强制更新时，用户点击取消也要更新
          console.log('强制更新，开始下载安装')
          this.downloadAndInstall(updateInfo)
        } else {
          console.log('用户选择稍后更新')
        }
      },
      fail: (error) => {
        console.error('显示更新对话框失败:', error)
      }
    })
  }

  /**
   * 下载并安装更新包
   * @param {Object} updateInfo 更新信息
   */
  downloadAndInstall(updateInfo) {
    const { wgtUrl, forceUpdate } = updateInfo
    
    if (!wgtUrl) {
      uni.showToast({
        title: '下载地址无效',
        icon: 'none'
      })
      return
    }

    // 显示下载进度
    uni.showLoading({
      title: '正在下载...'
    })

    // 下载更新包
    const downloadTask = uni.downloadFile({
      url: wgtUrl,
      success: (res) => {
        uni.hideLoading()
        
        if (res.statusCode === 200) {
          this.installUpdate(res.tempFilePath, forceUpdate, updateInfo)
        } else {
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      },
      fail: (error) => {
        uni.hideLoading()
        console.error('下载失败:', error)
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })

    // 监听下载进度
    downloadTask.onProgressUpdate((res) => {
      const progress = Math.round(res.progress)
      uni.showLoading({
        title: `下载中 ${progress}%`
      })
    })
  }

  /**
   * 安装更新包
   * @param {String} filePath 文件路径
   * @param {Boolean} forceUpdate 是否强制更新
   * @param {Object} updateInfo 更新信息
   */
  installUpdate(filePath, forceUpdate = false, updateInfo = null) {
    // #ifdef APP-PLUS
    plus.runtime.install(filePath, {
      force: false
    }, () => {
      // 安装成功
      const message = forceUpdate ? '更新完成，应用将重启' : '更新完成，是否重启应用？'

      // 记录更新的版本信息
      if (updateInfo && updateInfo.version) {
        try {
          uni.setStorageSync('app_last_update_version', updateInfo.version)
          uni.setStorageSync('app_update_time', new Date().getTime())
          console.log('记录更新版本:', updateInfo.version)
        } catch (error) {
          console.error('记录更新版本失败:', error)
        }
      }

      if (forceUpdate) {
        uni.showToast({
          title: message,
          icon: 'success',
          duration: 2000
        })

        setTimeout(() => {
          // 清除版本缓存，确保重启后获取最新版本
          this.clearVersionCache()
          plus.runtime.restart()
        }, 2000)
      } else {
        uni.showModal({
          title: '安装成功',
          content: message,
          showCancel: true,
          confirmText: '立即重启',
          cancelText: '稍后重启',
          success: (res) => {
            if (res.confirm) {
              // 清除版本缓存，确保重启后获取最新版本
              this.clearVersionCache()
              plus.runtime.restart()
            }
          }
        })
      }
    }, (error) => {
      // 安装失败
      console.error('安装失败:', error)
      uni.showModal({
        title: '安装失败',
        content: error.message || '安装过程中出现错误',
        showCancel: false
      })
    })
    // #endif
  }

  /**
   * 静默更新（后台下载，下次启动生效）
   * @param {Object} updateInfo 更新信息
   */
  silentUpdate(updateInfo) {
    const { wgtUrl } = updateInfo
    
    if (!wgtUrl) {
      console.error('静默更新失败: 下载地址无效')
      return
    }

    console.log('开始静默更新...')
    
    uni.downloadFile({
      url: wgtUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          // #ifdef APP-PLUS
          plus.runtime.install(res.tempFilePath, {
            force: false
          }, () => {
            console.log('静默更新安装成功，下次启动生效')
          }, (error) => {
            console.error('静默更新安装失败:', error)
          })
          // #endif
        }
      },
      fail: (error) => {
        console.error('静默更新下载失败:', error)
      }
    })
  }
}

// 创建单例实例
const appUpdate = new AppUpdate()

export default appUpdate
