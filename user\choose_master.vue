<template>
	<view class="page">
		<u-modal :show="show" :content='content' :showCancelButton="true" cancelText="再想想" @cancel="show= false"
			@confirm="confirmMaster"></u-modal>
		<view class="header">
			<view class="title">等待您选择师傅</view>
			<view class="desc">师傅可能在服务，请耐心等待</view>
			<view class="time">距师傅报价截止还剩：<u-count-down :time="24 * 60 * 60 * 1000" format="HH:mm:ss"></u-count-down>
			</view>
		</view>
		<view class="main">
			<scroll-view scroll-y="true">
				<view class="main_item" v-for="(item,index) in info.quotedPriceVos" :key="index">
					<view class="time">报价时间{{item.priceCreateTime}}</view>
					<view class="box">
						<image :src="item.selfImg?item.selfImg:'/static/mine/default_user.png'" mode="aspectFit"></image>
						<view class="mid">
							<view class="top">
								<view class="name">{{item.coachName}}</view>
								<view class="level" v-if="item.labelName != null">{{item.labelName}}</view>
								<view class="promise" v-if="item.cashPledge != null">已缴纳保证金</view>
							</view>
							<view class="bottom">服务<span>{{item.count}}</span>次</view>
						</view>
						<view class="price">￥{{item.price.toFixed(2) }}</view>
					</view>
					<view class="down">
						<view class="btn" @click="chooseOne(item)">选择他</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="footer">
			<view class="btn" @click="cancelO">取消订单</view>
		</view>

		<u-modal :show="showCancel" title="取消订单" content='确认要取消该订单吗' showCancelButton @cancel="showCancel = false"
			@confirm="confirmCancel"></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: false,jiaNum:0,
				content: '确认选择这个师傅的报价吗？',
				info: {},
				showCancel: false,
				que_id: '',
				selectPrice: '',
			}
		},
		methods: {
			// getcommissionRatio(){
			// 	this.$api.service.commissionRatio().then(res=>{
			// 		console.log(res)
			// 		this.jiaNum=res.data
			// 		console.log(this.jiaNum)
			// 	})
			// },
			chooseOne(item) {
				console.log('Selected item:', item)
				this.show = true
				this.que_id = item.id
				
				this.selectPrice =  parseFloat((item.price * (1 + this.jiaNum / 100)).toFixed(2));
			},
			confirmMaster() {
				this.show = false
				console.log(this.info)
				console.log('Confirming master with price:', this.selectPrice, 'and ID:', this.que_id)
				this.$api.service.choosePrice({
					orderPrice: this.selectPrice.toFixed(2),
					quotedPriceId: this.que_id
				}).then(res => {
					console.log('API response:', res)
					uni.showToast({
						icon: 'success',
						title: '选择成功,请完成支付'
					})
					setTimeout(() => {
						// console.log('Emitting cancelOr event')
						uni.$emit('cancelOr')
						uni.redirectTo({
							// url:'/user/order_list?tab=1'
							url:`/user/Cashier?id=${this.info.id}&price=${this.info.selectPrice}&type=${this.info.payType}&goodsId=${this.info.goodsId}`
						})
					}, 2000)
				}).catch(err => {
					console.error('API error:', err)
					uni.showToast({
						icon: 'error',
						title: '选择失败'
					})
					
				})
			},
			cancelO() {
				this.showCancel = true
			},
			confirmCancel() {
				this.showCancel = false
				console.log('Cancelling order with ID:', this.info.id)
				this.$api.service.cancelOrder({
					id: this.info.id
				}).then(res => {
					uni.showToast({
						icon: 'none',
						title: '取消成功'
					})
					console.log('Emitting cancelOr event for cancel')
					uni.$emit('cancelOr')
					uni.navigateBack()
				}).catch(err => {
					console.error('Cancel error:', err)
					uni.showToast({
						icon: 'error',
						title: '取消失败'
					})
				})
			},
		},
		onLoad() {
			this.info = this.$store.state.service.orderInfo
			console.log('Loaded order info:', this.info)
			// this.getcommissionRatio()
		}
	}
</script>

<style scoped lang="scss">
	.page {
		background: #f3f4f5;
		height: 100vh;

		.header {
			padding: 40rpx 32rpx;

			.title {
				font-size: 40rpx;
				font-weight: 500;
				color: #333333;
			}

			.desc {
				margin-top: 20rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #ADADAD;
			}

			.time {
				display: flex;
				align-items: center;
				margin-top: 20rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #ADADAD;

				::v-deep .u-count-down__text {
					color: #E72427;
				}
			}
		}

		.main {
			width: 750rpx;
			height: 82vh;
			padding: 0 32rpx;
			background: #ffffff;
			border-radius: 40rpx 40rpx 0rpx 0rpx;
			padding-bottom: 192rpx;

			scroll-view {
				height: 100%;

				.main_item {
					padding: 40rpx 0;
					border-bottom: 2rpx solid #F2F3F6;

					.time {
						font-size: 24rpx;
						font-weight: 400;
						color: #ADADAD;
					}

					.box {
						margin-top: 20rpx;
						display: flex;

						image {
							width: 100rpx;
							height: 100rpx;
							border-radius: 50%;
						}

						.mid {
							margin-left: 20rpx;

							.top {
								padding-top: 4rpx;
								display: flex;
								align-items: center;

								.name {
									font-size: 28rpx;
									font-weight: 500;
									color: #333333;
								}

								.level,
								.promise {
									margin-left: 8rpx;
									width: fit-content;
									height: 28rpx;
									background: #2E80FE;
									border-radius: 14rpx 14rpx 14rpx 14rpx;
									padding: 0 14rpx;
									line-height: 28rpx;
									text-align: center;
									font-size: 16rpx;
									font-weight: 400;
									color: #FFFFFF;
								}
							}

							.bottom {
								margin-top: 20rpx;
								font-size: 24rpx;
								font-weight: 400;
								color: #ADADAD;

								span {
									color: #333333;
								}
							}
						}

						.price {
							flex: 1;
							font-size: 40rpx;
							font-weight: 600;
							color: #E72427;
							text-align: right;
						}
					}

					.down {
						margin-top: 32rpx;
						display: flex;
						flex-direction: row-reverse;

						.btn {
							width: 240rpx;
							height: 80rpx;
							background: #2E80FE;
							border-radius: 50rpx 50rpx 50rpx 50rpx;
							font-size: 28rpx;
							font-weight: 500;
							color: #FFFFFF;
							line-height: 80rpx;
							text-align: center;
						}
					}
				}
			}
		}

		.footer {
			width: 750rpx;
			height: 192rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			bottom: 0;
			background-color: #FFFFFF;

			.btn {
				width: 686rpx;
				height: 88rpx;
				background: #2E80FE;
				border-radius: 44rpx 44rpx 44rpx 44rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 88rpx;
			 text-align: center;
			}
		}
	}
</style>