<template>
  <view class="test-page">
    <view class="header">
      <text class="title">热更新功能测试</text>
    </view>

    <view class="test-section">
      <view class="section-title">当前版本信息</view>
      <view class="info-item">
        <text class="label">版本号:</text>
        <text class="value">{{ currentVersion }}</text>
      </view>
      <view class="info-item">
        <text class="label">平台类型:</text>
        <text class="value">{{ platform === 2 ? '用户端' : '师傅端' }}</text>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">测试功能</view>
      
      <button class="test-btn" @click="testCheckUpdate" :disabled="isChecking">
        {{ isChecking ? '检查中...' : '测试版本检查' }}
      </button>
      
      <button class="test-btn" @click="testSilentCheck" :disabled="isChecking">
        {{ isChecking ? '检查中...' : '测试静默检查' }}
      </button>
      
      <button class="test-btn" @click="testForceUpdate">
        模拟强制更新
      </button>
      
      <button class="test-btn" @click="testOptionalUpdate">
        模拟可选更新
      </button>
      
      <button class="test-btn" @click="navigateToUpdatePage">
        打开更新页面
      </button>
    </view>

    <view class="test-section">
      <view class="section-title">测试日志</view>
      <scroll-view class="log-container" scroll-y>
        <view class="log-item" v-for="(log, index) in logs" :key="index">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-content">{{ log.content }}</text>
        </view>
      </scroll-view>
      <button class="clear-btn" @click="clearLogs">清空日志</button>
    </view>
  </view>
</template>

<script>
import appUpdate from '@/utils/app-update.js'

export default {
  name: 'UpdateTest',
  data() {
    return {
      currentVersion: '1.0.0',
      platform: 2,
      isChecking: false,
      logs: []
    }
  },
  onLoad() {
    this.getCurrentVersion()
    this.addLog('页面加载完成')
  },
  methods: {
    /**
     * 获取当前版本
     */
    async getCurrentVersion() {
      this.currentVersion = await appUpdate.getCurrentVersion()
      this.addLog(`获取当前版本: ${this.currentVersion}`)
    },

    /**
     * 测试版本检查
     */
    async testCheckUpdate() {
      this.isChecking = true
      this.addLog('开始测试版本检查...')
      
      try {
        await appUpdate.checkUpdate({
          silent: false,
          showLoading: true
        })
        this.addLog('版本检查完成')
      } catch (error) {
        this.addLog(`版本检查失败: ${error}`)
      } finally {
        this.isChecking = false
      }
    },

    /**
     * 测试静默检查
     */
    async testSilentCheck() {
      this.isChecking = true
      this.addLog('开始测试静默检查...')
      
      try {
        await appUpdate.checkUpdate({
          silent: true,
          showLoading: false
        })
        this.addLog('静默检查完成')
      } catch (error) {
        this.addLog(`静默检查失败: ${error}`)
      } finally {
        this.isChecking = false
      }
    },

    /**
     * 模拟强制更新
     */
    testForceUpdate() {
      this.addLog('模拟强制更新场景')
      
      const mockUpdateInfo = {
        latestVersion: '1.2.0',
        description: '这是一个模拟的强制更新，包含重要安全修复',
        wgtUrl: 'https://example.com/mock_update.wgt',
        forceUpdate: 1
      }
      
      appUpdate.showUpdateDialog(mockUpdateInfo)
      this.addLog('显示强制更新对话框')
    },

    /**
     * 模拟可选更新
     */
    testOptionalUpdate() {
      this.addLog('模拟可选更新场景')
      
      const mockUpdateInfo = {
        latestVersion: '1.1.5',
        description: '这是一个模拟的可选更新，优化了用户体验',
        wgtUrl: 'https://example.com/mock_update.wgt',
        forceUpdate: 0
      }
      
      appUpdate.showUpdateDialog(mockUpdateInfo)
      this.addLog('显示可选更新对话框')
    },

    /**
     * 打开更新页面
     */
    navigateToUpdatePage() {
      this.addLog('导航到更新页面')
      uni.navigateTo({
        url: '/pages/app-update',
        success: () => {
          this.addLog('成功打开更新页面')
        },
        fail: (error) => {
          this.addLog(`打开更新页面失败: ${JSON.stringify(error)}`)
        }
      })
    },

    /**
     * 添加日志
     */
    addLog(content) {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      
      this.logs.unshift({
        time,
        content
      })
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
      
      console.log(`[${time}] ${content}`)
    },

    /**
     * 清空日志
     */
    clearLogs() {
      this.logs = []
      this.addLog('日志已清空')
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  padding: 40rpx 0;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.test-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    border-bottom: 2rpx solid #eee;
    padding-bottom: 10rpx;
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    color: #666;
    font-size: 28rpx;
  }
  
  .value {
    color: #333;
    font-size: 28rpx;
    font-weight: 500;
  }
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:disabled {
    background: #ccc;
  }
  
  &:active:not(:disabled) {
    opacity: 0.8;
  }
}

.log-container {
  height: 400rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  background: #fafafa;
  
  .log-item {
    display: flex;
    margin-bottom: 10rpx;
    font-size: 24rpx;
    
    .log-time {
      color: #999;
      margin-right: 20rpx;
      min-width: 120rpx;
    }
    
    .log-content {
      color: #333;
      flex: 1;
      word-break: break-all;
    }
  }
}

.clear-btn {
  width: 100%;
  height: 60rpx;
  background: #f56565;
  color: #fff;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-top: 20rpx;
  
  &:active {
    opacity: 0.8;
  }
}
</style>
