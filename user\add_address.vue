
<template>
	<view class="page">
		<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler"
			keyName="title" @cancel="showCity = false" @confirm="confirmCity" v-if="flag"></u-picker>
		<view class="top">个人信息隐私信息完全保密</view>
		<view class="main">
			<view class="main_item " @tap="goMap">
				<view class="name">服务地址</view>
				<view class="address">
					<span>{{form.address}}</span>
				</view>
				<image src="../static/images/position.png" mode=""></image>
				</view>
			<!-- 备用位置获取按钮，当主要方法失败时显示 -->
			
		<view class="main_item">
				<view class="name">门牌号</view>
				<input type="text" v-model="form.houseNumber" placeholder="请输入详细地址，如7栋4单元18a">
			</view>
			<view class="main_item">
				<view class="name">联系人</view>
				<input type="text" v-model="form.userName" placeholder="请输入姓名">
			</view>
			<view class="main_item">
				<view class="name">性别</view>
				<view class="box">
					<view class="box_item"
						:style="form.sex == 1?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''"
						@click="selectGender(1)">先生</view>
					<view class="box_item"
						:style="form.sex == 2?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''"
						@click="selectGender(2)">女士</view>
				</view>
			</view>
			<view class="main_item">
				<view class="name">手机号码</view>
				<input type="tel" v-model="form.mobile" placeholder="请输入手机号码">
			</view>
			<view class="main_item last">
				<view class="name">设为默认地址</view>
				<u-switch v-model="form.status" activeColor="#2E80FE"></u-switch>
			</view>
		</view>
		<view class="btn" @click="SaveAddress">保存</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				flag: false,
				loading: false,
				showCity: false,
				menpai: '',
				
				form: {
					userName: '',
					mobile: '',
					address: '点击选择服务地址',
					addressInfo: '',
					houseNumber: '',
					city: '',
					cityIds: '',
					lng: '',
					lat: '',
				
					sex: 1,
					status: false,
					provinceId: 0,
					cityId: 0,
					areaId: 0
				},
				columnsCity: [
					[], // Province
					[], // City
					[]  // Area
				],
			}
		},
		onLoad() {
			// this.getCity(0)
			this.getNowPosition()
			this.checkAppVersion()
		},
		methods: {
			// 性别选择方法
			selectGender(gender) {
				this.form.sex = gender;
				console.log('Selected gender:', gender);
			},
			// 解析城市信息的通用方法
			parseCityInfo(address) {
				if (!address || typeof address !== 'string') {
					return { cityIds: '', city: '' };
				}

				// 处理各种地址格式的正则表达式
				const patterns = [
					// 标准格式：省+市+区/县
					/^(.+?省)(.+?市)(.+?[县区]).*$/,
					// 自治区格式：自治区+市+区/县/旗
					/^(.+?自治区)(.+?市)(.+?[县区旗]).*$/,
					// 自治区+盟+市格式：自治区+盟+市
					/^(.+?自治区)(.+?盟)(.+?市).*$/,
					// 直辖市格式：市+区/县
					/^(北京|上海|天津|重庆)(市)?(.+?[县区]).*$/,
					// 特别行政区格式
					/^(香港|澳门)(.+?区)?(.*)$/
				];

				for (let pattern of patterns) {
					const match = address.match(pattern);
					if (match) {
						let province, city, area;

						if (pattern.source.includes('北京|上海|天津|重庆')) {
							// 直辖市处理
							province = match[1];
							city = match[1] + '市';
							area = match[3] || '';
						} else if (pattern.source.includes('香港|澳门')) {
							// 特别行政区处理
							province = match[1];
							city = match[1];
							area = match[2] || match[3] || '';
						} else if (pattern.source.includes('盟')) {
							// 自治区+盟+市格式处理
							province = match[1];
							city = match[2];  // 盟作为市级
							area = match[3];  // 市作为区级
						} else {
							// 标准省市区处理
							province = match[1];
							city = match[2];
							area = match[3];
						}

						// 清理空白字符
						province = province.trim();
						city = city.trim();
						area = area.trim();

						return {
							cityIds: `${province},${city},${area}`,
							city: `${province}-${city}-${area}`
						};
					}
				}

				// 如果都不匹配，返回空值
				console.warn('无法解析地址格式:', address);
				return { cityIds: '', city: '' };
			},

			getNowPosition() {
				return new Promise((resolve) => {
					uni.getLocation({
						type: "gcj02",
						isHighAccuracy: true,
						accuracy: "best",
						success: (res) => {
							uni.setStorageSync("lat", res.latitude);
							uni.setStorageSync("lng", res.longitude);
							uni.request({
								url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
								success: (res1) => {
									console.log(res1)
									this.form.address = res1.data.regeocode.formatted_address
									// 使用新的城市信息解析方法
									const cityInfo = this.parseCityInfo(res1.data.regeocode.formatted_address);
									this.form.cityIds = cityInfo.cityIds;
									this.form.city = cityInfo.city;
									// Store coordinates
									this.form.lng = res.longitude;
									this.form.lat = res.latitude;
									resolve();
								},
								fail: (err) => {
									console.error("逆地理编码失败:", err);
									resolve();
								}
							});
						},
						fail: (err) => {
							console.error("获取定位失败:", err);
							resolve();
						}
					});
				});
			},
			
			
			
			
			goMap() {
				let that = this
				// #ifdef MP-WEIXIN
				uni.authorize({
					scope: 'scope.userLocation',
					success(res) {
						// 添加延时和错误处理
						setTimeout(() => {
							uni.chooseLocation({
								success: function(res) {
									console.log('选择位置成功:', res);
									try {
										// Split address into province, city, county format, discarding anything after county
										// Use 'that' to correctly reference 'this.form.cityIds'
										that.form.cityIds = res.address
										  ? that.parseCityInfo(res.address).cityIds
										  : '';
										that.form.city = res.address
										  ? that.parseCityInfo(res.address).city
										  : '';
										console.log('处理后的cityIds:', that.form.cityIds);
										console.log('处理后的city:', that.form.city);

										that.form.address = res.name || '未知位置'
										that.form.addressInfo = res.address || ''
										that.form.lng = res.longitude || ''
										that.form.lat = res.latitude || ''

										uni.showToast({
											title: '位置选择成功',
											icon: 'success',
											duration: 1500
										});
									} catch (error) {
										console.error('处理位置信息时出错:', error);
										uni.showToast({
											title: '位置信息处理失败',
											icon: 'none',
											duration: 2000
										});
									}
								},
								fail: function(err) {
									console.error('选择位置失败:', err);
									uni.showToast({
										title: '选择位置失败，请重试',
										icon: 'none',
										duration: 2000
									});
								}
							});
						}, 300); // 延时300ms避免框架内部状态问题
					},
					fail(err) {
						console.error('位置授权失败:', err)
						uni.showToast({
							title: '请授权位置信息',
							icon: 'none',
							duration: 2000
						});
					}
				})
				// #endif
				// #ifdef APP
				// APP端需要更长的延时来避免内部状态问题
				setTimeout(() => {
					try {
						uni.chooseLocation({
							success: function(res) {
								console.log('APP选择位置成功:', res)
								try {
									// 严格检查res对象的完整性，包括pageIndex属性
									if (!res || typeof res !== 'object') {
										throw new Error('返回的位置信息格式异常');
									}

									// 检查是否存在pageIndex为null的情况（这是导致错误的根本原因）
									if (res.hasOwnProperty('pageIndex') && res.pageIndex === null) {
										console.warn('检测到pageIndex为null，但位置信息正常，继续处理');
									}

									// APP端也需要处理cityIds和city字段，与小程序端保持一致
									if (res.address && typeof res.address === 'string') {
										const cityInfo = that.parseCityInfo(res.address);
										that.form.cityIds = cityInfo.cityIds;
										that.form.city = cityInfo.city;
									} else {
										that.form.cityIds = '';
										that.form.city = '';
									}

									// 安全地设置各个字段
									that.form.address = (res.name && typeof res.name === 'string') ? res.name : '选择的位置'
									that.form.addressInfo = (res.address && typeof res.address === 'string') ? res.address : ''
									that.form.lng = (res.longitude && typeof res.longitude === 'number') ? res.longitude.toString() : ''
									that.form.lat = (res.latitude && typeof res.latitude === 'number') ? res.latitude.toString() : ''

									console.log('APP端处理后的cityIds:', that.form.cityIds);
									console.log('APP端处理后的city:', that.form.city);

									uni.showToast({
										title: '位置选择成功',
										icon: 'success',
										duration: 1500
									});
								} catch (error) {
									console.error('APP处理位置信息时出错:', error);
									// 即使处理出错，也要设置基本信息
									that.form.address = '位置信息'
									that.form.addressInfo = ''
									that.form.lng = ''
									that.form.lat = ''

									uni.showToast({
										title: '位置信息处理失败，请重新选择',
										icon: 'none',
										duration: 2500
									});
								}
							},
							fail: function(err) {
								console.error('APP选择位置失败:', err);

								// 检查是否是pageIndex相关的错误
								if (err && err.errMsg && (err.errMsg.includes('pageIndex') || err.errMsg.includes('null'))) {
									console.error('检测到pageIndex为null的错误，这是HBuilderX版本兼容性问题');
									uni.showToast({
										title: '地图组件异常，请使用"获取当前位置"功能',
										icon: 'none',
										duration: 3500
									});
									return;
								}

								// 区分不同的错误类型
								if (err && err.errMsg) {
									if (err.errMsg.includes('cancel')) {
										// 用户取消选择，不显示错误提示
										console.log('用户取消了位置选择');
										return;
									} else if (err.errMsg.includes('auth')) {
										uni.showToast({
											title: '请授权位置权限',
											icon: 'none',
											duration: 2500
										});
									} else {
										uni.showToast({
											title: '位置选择失败，建议使用"获取当前位置"',
											icon: 'none',
											duration: 3000
										});
									}
								} else {
									uni.showToast({
										title: '地图功能异常，请尝试其他方式',
										icon: 'none',
										duration: 2500
									});
								}
							}
						});
					} catch (globalError) {
						console.error('APP端chooseLocation调用失败:', globalError);
						uni.showToast({
							title: '地图功能暂时不可用',
							icon: 'none',
							duration: 2000
						});
					}
				}, 500); // APP端使用更长的延时
				// #endif
			},

			// 备用的位置获取方法
			async getLocationFallback() {
				try {
					const res = await uni.getLocation({
						type: 'gcj02',
						isHighAccuracy: true,
						accuracy: 'best'
					});

					// 使用高德地图API进行逆地理编码
					const geoRes = await uni.request({
						url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
						method: 'GET'
					});

					if (geoRes.data && geoRes.data.regeocode) {
						this.form.address = geoRes.data.regeocode.formatted_address || '当前位置';
						this.form.addressInfo = geoRes.data.regeocode.formatted_address || '';
						this.form.lng = res.longitude;
						this.form.lat = res.latitude;

						// 处理城市信息
						if (geoRes.data.regeocode.formatted_address) {
							const cityInfo = this.parseCityInfo(geoRes.data.regeocode.formatted_address);
							this.form.cityIds = cityInfo.cityIds;
							this.form.city = cityInfo.city;
						}

						uni.showToast({
							title: '已获取当前位置',
							icon: 'success',
							duration: 1500
						});
					}
				} catch (error) {
					console.error('备用位置获取失败:', error);
					uni.showToast({
						title: '位置获取失败，请手动输入',
						icon: 'none',
						duration: 2000
					});
				}
			},
			confirmCity(Array) {
				// Map selected values to titles and IDs
				const selectedValues = Array.value
				const titles = selectedValues.map((item, index) => {
					return item?.title || this.columnsCity[index][0]?.title || ''
				})
				const ids = selectedValues.map((item, index) => {
					return item?.id || this.columnsCity[index][0]?.id || 0
				}).filter(id => id !== null && id !== undefined)

				this.form.city = titles.join('-')
				// Set cityIds as nested array [[provinceId, cityId, areaId]]
				this.form.cityIds = ids.length >= 3 ? [[ids[0], ids[1], ids[2]]] : [[0, 0, 0]]
				// Set individual IDs
				this.form.provinceId = ids[0] || 0
				this.form.cityId = ids[1] || 0
				this.form.areaId = ids[2] || 0
				this.showCity = false
			},
			getCity(e) {
				this.$api.service.getCity(e).then(res => {
					this.columnsCity[0] = res
					if (res[0]?.id) {
						this.$api.service.getCity(res[0].id).then(res1 => {
							this.columnsCity[1] = res1
							if (res1[0]?.id) {
								this.$api.service.getCity(res1[0].id).then(res2 => {
									this.columnsCity[2] = res2
									this.flag = true
									
								})
							}
						})
					}
				}).catch(err => {
					console.error('Failed to fetch city data:', err)
				})
			},
			changeHandler(e) {
				const {
					columnIndex,
					index,
					picker = this.$refs.uPicker
				} = e
				if (columnIndex === 0) {
					if (this.columnsCity[0][index]?.id) {
						this.$api.service.getCity(this.columnsCity[0][index].id).then(res => {
							picker.setColumnValues(1, res)
							this.columnsCity[1] = res
							if (res[0]?.id) {
								this.$api.service.getCity(res[0].id).then(res1 => {
									picker.setColumnValues(2, res1)
									this.columnsCity[2] = res1
										console.log(res1)
								})
							}
						})
					}
				} else if (columnIndex === 1) {
					if (this.columnsCity[1][index]?.id) {
						this.$api.service.getCity(this.columnsCity[1][index].id).then(res => {
							picker.setColumnValues(2, res)
							this.columnsCity[2] = res
							console.log(res)
						})
					}
				}
			},
			async SaveAddress() {
				if (this.form.address === '点击选择服务地址') {
					uni.showToast({
						icon: 'none',
						title: '请填写完整提交',
						findduration: 1500
					})
					return
				}
				let phoneReg = /^1[3456789]\d{9}$/
				if (!phoneReg.test(this.form.mobile)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的手机号',
						duration: 1500
					})
					return
				}
				console.log(this.form)
				for (let key of ['userName', 'mobile', 'address', 'houseNumber']) {
					if (this.form[key] === '') {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交',
							duration: 1500
						})
						return
					}
				}
				// 检查城市信息是否有效
				if (!this.form.cityIds || this.form.cityIds === '') {
					uni.showToast({
						icon: 'none',
						title: '请选择所在区域',
						duration: 1500
					})
					return
				}
				if (this.form.cityIds) { // Only call if cityIds has a value from goMap
				  try {
				    const res = await this.$api.service.getZhuanhuan({
				      mergeName: this.form.cityIds
				    });
				    console.log(res);
				    if (res.data) {
				      // Construct the comma-separated string from the individual IDs
				      this.form.cityIds = `${res.data.provinceId},${res.data.cityId},${res.data.areaId}`;
				console.log(this.form.cityIds)
				    } else {
				      this.form.cityIds = ''; // Handle cases where res.data might be null or undefined
				    }
				  } catch (err) {
				    console.error("Error converting cityIds:", err);
				    uni.showToast({
				      icon: 'none',
				      title: '城市信息转换失败',
				      duration: 1500,
				    });
				    return; // Stop execution if conversion fails
				  }
				}
				// Prepare form data for API
				let userId= uni.getStorageSync('userInfo')
				console.log(userId)
				console.log(this.form)
				// console.log(JSON.parse(userId))
				let subForm = {
					address: this.form.address,
					addressInfo: this.form.addressInfo,
					// areaId: this.form.areaId,
					city: this.form.city,
					// cityId: this.form.cityId,
					cityIds: this.form.cityIds, // [[provinceId, cityId, areaId]]
					// createTime: 0,
					houseNumber: this.form.houseNumber,
					// id: 0,
					lat: this.form.lat,
					lng: this.form.lng,
					mobile: this.form.mobile,
					// provinceId: this.form.provinceId,
					sex: this.form.sex,
					status: this.form.status ? 1 : 0,
					// top: 0,
					// uniacid: this.form.uniacid,
					
					// userId:userId.userId,
					userName: this.form.userName
				}
				try {
					const res = await this.$api.service.postAddAddress(subForm);
					console.log('Add address response:', res);
					if(res.code === '200'){
						uni.showToast({
							icon: 'success',
							title: '提交成功',
							duration: 1000
						})
						setTimeout(() => {
							uni.navigateBack({ delta: 1 })
						}, 1000)
					} else {
						console.error('Add address failed with code:', res.code, 'message:', res.msg);
						uni.showToast({
							icon: 'none',
							title: res.msg || '提交失败，请重新尝试',
							duration: 1500
						})
					}
				} catch (err) {
					console.error('Add address error:', err);
					uni.showToast({
						icon: 'none',
						title: err.msg || err.message || '网络错误，请重试',
						duration: 1500
					})
				}
			},

			// 检查APP版本和环境
			checkAppVersion() {
				// #ifdef APP
				try {
					const systemInfo = uni.getSystemInfoSync();
					console.log('系统信息:', systemInfo);

					// 如果是开发环境，提示版本问题
					if (systemInfo.platform === 'android' || systemInfo.platform === 'ios') {
						console.warn('当前使用的HBuilderX版本可能存在chooseLocation的bug');
						console.warn('建议降级到HBuilderX 4.45或3.8.12版本');
					}
				} catch (error) {
					console.error('获取系统信息失败:', error);
				}
				// #endif
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		height: 100vh;
		background-color: #fff;

		.top {
			width: 750rpx;
			height: 58rpx;
			background: #FFF7F1;
			font-size: 28rpx;
			font-weight: 400;
			color: #FE921B;
			line-height: 58rpx;
			text-align: center;
		}

		.btn {
			margin: 0 auto;
			margin-top: 88rpx;
			width: 690rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 98rpx;
			text-align: center;
		}

		.main {
			padding: 0 30rpx;

			.main_item {
				padding: 40rpx 0;
				border-bottom: 2rpx solid #E9E9E9;
				display: flex;
				align-items: center;
				position: relative;

				.name {
					min-width: 112rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;
					margin-right: 40rpx;
				}

				.address {
					font-size: 28rpx;
					font-weight: 400;
					color: #ADADAD;

					.details {
						margin-top: 20rpx;
						font-size: 20rpx;
						font-weight: 400;
						color: #ADADAD;
					}
				}

				image {
					width: 23rpx;
					height: 27rpx;
					position: absolute;
					right: 0;
					top: 46rpx;
				}

				input {
					width: 450rpx;
				}

				.box {
					display: flex;
					align-items: center;

					.box_item {
						margin-right: 20rpx;
						width: 88rpx;
						height: 50rpx;
						background: #FFFFFF;
						border-radius: 4rpx;
						border: 2rpx solid #EDEDED;
						font-size: 28rpx;
						font-weight: 400;
						color: #ADADAD;
						line-height: 46rpx;
						text-align: center;
					}
				}
			}

			.last {
				justify-content: space-between;

				.name {
					width: 170rpx;
				}
			}
		}

		.fallback_location {
			padding: 20rpx 30rpx;
			text-align: center;

			.fallback_text {
				font-size: 24rpx;
				color: #2E80FE;
				text-decoration: underline;
			}
		}
	}
</style>
```